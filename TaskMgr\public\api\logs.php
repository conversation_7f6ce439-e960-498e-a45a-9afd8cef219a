<?php
/**
 * 日志查询API接口
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 自动加载类文件
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/../../src/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 错误处理
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message], JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = 'success') {
    echo json_encode(['success' => true, 'message' => $message, 'data' => $data], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $db = Database::getInstance();
    $logger = new Logger();
    $method = $_SERVER['REQUEST_METHOD'];
    $pathInfo = $_SERVER['PATH_INFO'] ?? '';
    
    if ($method !== 'GET') {
        sendError('只支持GET请求', 405);
    }
    
    // 解析路径参数
    $pathParts = array_filter(explode('/', $pathInfo));
    $action = isset($pathParts[0]) ? $pathParts[0] : 'task-logs';
    
    switch ($action) {
        case 'task-logs':
            // 获取任务执行日志
            $taskId = isset($_GET['task_id']) ? (int)$_GET['task_id'] : null;
            $status = $_GET['status'] ?? null;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
            $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
            $startDate = $_GET['start_date'] ?? null;
            $endDate = $_GET['end_date'] ?? null;
            
            $sql = "SELECT tl.*, t.name as task_name 
                    FROM taskmgr_task_logs tl 
                    LEFT JOIN taskmgr_tasks t ON tl.task_id = t.id 
                    WHERE 1=1";
            $params = [];
            
            if ($taskId) {
                $sql .= " AND tl.task_id = ?";
                $params[] = $taskId;
            }
            
            if ($status) {
                $sql .= " AND tl.status = ?";
                $params[] = $status;
            }
            
            if ($startDate) {
                $sql .= " AND tl.start_time >= ?";
                $params[] = $startDate . ' 00:00:00';
            }
            
            if ($endDate) {
                $sql .= " AND tl.start_time <= ?";
                $params[] = $endDate . ' 23:59:59';
            }
            
            $sql .= " ORDER BY tl.start_time DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $logs = $db->fetchAll($sql, $params);
            
            // 获取总数
            $countSql = "SELECT COUNT(*) as total FROM taskmgr_task_logs tl WHERE 1=1";
            $countParams = [];
            
            if ($taskId) {
                $countSql .= " AND tl.task_id = ?";
                $countParams[] = $taskId;
            }
            
            if ($status) {
                $countSql .= " AND tl.status = ?";
                $countParams[] = $status;
            }
            
            if ($startDate) {
                $countSql .= " AND tl.start_time >= ?";
                $countParams[] = $startDate . ' 00:00:00';
            }
            
            if ($endDate) {
                $countSql .= " AND tl.start_time <= ?";
                $countParams[] = $endDate . ' 23:59:59';
            }
            
            $total = $db->fetch($countSql, $countParams)['total'];
            
            sendSuccess([
                'logs' => $logs,
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset
            ]);
            break;
            
        case 'system-logs':
            // 获取系统日志
            $date = $_GET['date'] ?? date('Y-m-d');
            $level = $_GET['level'] ?? null;
            $keyword = $_GET['keyword'] ?? null;
            $lines = isset($_GET['lines']) ? (int)$_GET['lines'] : 100;
            
            if ($keyword) {
                // 搜索日志
                $results = $logger->searchLogs($keyword, $level, $date, $lines);
                sendSuccess($results);
            } else {
                // 读取日志文件
                $filename = 'taskmgr_' . $date . '.log';
                try {
                    $content = $logger->readLogFile($filename, $lines);
                    sendSuccess($content);
                } catch (Exception $e) {
                    sendError('日志文件不存在或无法读取');
                }
            }
            break;
            
        case 'log-files':
            // 获取日志文件列表
            $files = $logger->getLogFiles();
            sendSuccess($files);
            break;
            
        case 'log-stats':
            // 获取日志统计
            $date = $_GET['date'] ?? date('Y-m-d');
            $stats = $logger->getLogStats($date);
            
            // 获取任务执行统计
            $taskStats = $db->fetch("
                SELECT 
                    COUNT(*) as total_executions,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
                    SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_count,
                    AVG(duration) as avg_duration
                FROM taskmgr_task_logs 
                WHERE DATE(start_time) = ?
            ", [$date]);
            
            sendSuccess([
                'system_logs' => $stats,
                'task_executions' => $taskStats
            ]);
            break;
            
        case 'recent-errors':
            // 获取最近的错误日志
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            
            $sql = "SELECT tl.*, t.name as task_name 
                    FROM taskmgr_task_logs tl 
                    LEFT JOIN taskmgr_tasks t ON tl.task_id = t.id 
                    WHERE tl.status IN ('failed', 'timeout') 
                    ORDER BY tl.start_time DESC 
                    LIMIT ?";
            
            $errors = $db->fetchAll($sql, [$limit]);
            sendSuccess($errors);
            break;
            
        case 'execution-detail':
            // 获取执行详情
            $executionId = $_GET['execution_id'] ?? null;
            $logId = isset($_GET['log_id']) ? (int)$_GET['log_id'] : null;
            
            if ($executionId) {
                $sql = "SELECT tl.*, t.name as task_name, t.command 
                        FROM taskmgr_task_logs tl 
                        LEFT JOIN taskmgr_tasks t ON tl.task_id = t.id 
                        WHERE tl.execution_id = ?";
                $log = $db->fetch($sql, [$executionId]);
            } elseif ($logId) {
                $sql = "SELECT tl.*, t.name as task_name, t.command 
                        FROM taskmgr_task_logs tl 
                        LEFT JOIN taskmgr_tasks t ON tl.task_id = t.id 
                        WHERE tl.id = ?";
                $log = $db->fetch($sql, [$logId]);
            } else {
                sendError('缺少execution_id或log_id参数');
            }
            
            if (!$log) {
                sendError('执行记录不存在', 404);
            }
            
            sendSuccess($log);
            break;
            
        default:
            sendError('无效的操作', 404);
    }
    
} catch (Exception $e) {
    $logger = new Logger();
    $logger->error('日志API错误', [
        'method' => $_SERVER['REQUEST_METHOD'],
        'uri' => $_SERVER['REQUEST_URI'],
        'error' => $e->getMessage()
    ]);
    
    sendError('服务器内部错误', 500);
}
