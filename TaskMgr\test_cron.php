<?php
/**
 * Cron测试页面
 * 帮助用户验证系统Cron是否正常工作
 */

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'taskmgr',
    'username' => 'root',
    'password' => 'YC@yc110',
    'charset' => 'utf8mb4'
];

// 数据库连接
function getDB() {
    global $db_config;
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['database']};charset={$db_config['charset']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    return $pdo;
}

// 处理创建测试任务
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test'])) {
    try {
        $pdo = getDB();
        
        // 创建一个每分钟执行的测试任务
        $name = "Cron测试任务 - " . date('H:i:s');
        $command = "echo 'Cron测试成功 - " . date('Y-m-d H:i:s') . "'";
        $cronExpression = "* * * * *"; // 每分钟执行
        
        $stmt = $pdo->prepare("INSERT INTO tasks (name, command, cron_expression, status) VALUES (?, ?, ?, 'enabled')");
        $stmt->execute([$name, $command, $cronExpression]);
        
        $testTaskId = $pdo->lastInsertId();
        $message = "✅ 测试任务创建成功！任务ID: $testTaskId";
        $messageType = "success";
        
    } catch (Exception $e) {
        $message = "❌ 创建测试任务失败: " . $e->getMessage();
        $messageType = "error";
    }
}

// 处理删除测试任务
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup_test'])) {
    try {
        $pdo = getDB();
        
        // 删除所有测试任务
        $stmt = $pdo->prepare("DELETE FROM tasks WHERE name LIKE 'Cron测试任务%'");
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        
        // 删除测试任务的日志
        $stmt = $pdo->prepare("DELETE FROM task_logs WHERE task_id NOT IN (SELECT id FROM tasks)");
        $stmt->execute();
        
        $message = "🗑️ 已清理 $deletedCount 个测试任务";
        $messageType = "success";
        
    } catch (Exception $e) {
        $message = "❌ 清理测试任务失败: " . $e->getMessage();
        $messageType = "error";
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cron功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #f8f9fa;
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🧪 Cron功能测试</h1>
        <p>这个页面帮助您测试系统Cron是否正常工作</p>
        <p><a href="index.php">← 返回主界面</a></p>
    </div>

    <?php if (isset($message)): ?>
    <div class="alert alert-<?= $messageType ?>">
        <?= $message ?>
    </div>
    <?php endif; ?>

    <!-- 系统Cron状态检查 -->
    <div class="card">
        <h2>📋 系统Cron状态</h2>
        <?php
        $cronCheck = shell_exec('crontab -l 2>/dev/null | grep "' . dirname(__FILE__) . '/index.php"');
        if ($cronCheck) {
            echo '<div class="alert alert-success">';
            echo '✅ 系统Cron已设置<br>';
            echo '<strong>当前设置:</strong><br>';
            echo '<code>' . htmlspecialchars(trim($cronCheck)) . '</code>';
            echo '</div>';
        } else {
            echo '<div class="alert alert-error">';
            echo '❌ 系统Cron未设置<br>';
            echo '<strong>需要执行:</strong> <code>./setup_cron.sh</code><br>';
            echo '或手动添加: <code>* * * * * /usr/bin/php ' . __FILE__ . ' cron</code>';
            echo '</div>';
        }
        ?>
    </div>

    <!-- 测试任务管理 -->
    <div class="card">
        <h2>🔬 创建测试任务</h2>
        <p>创建一个每分钟执行的测试任务，验证Cron是否正常工作</p>
        
        <form method="POST" style="margin-bottom: 20px;">
            <button type="submit" name="create_test" class="btn-success">
                ➕ 创建测试任务（每分钟执行）
            </button>
        </form>
        
        <div class="alert alert-info">
            <strong>测试步骤：</strong><br>
            1. 点击"创建测试任务"按钮<br>
            2. 等待1-2分钟<br>
            3. 刷新页面查看执行日志<br>
            4. 如果看到执行记录，说明Cron正常工作<br>
            5. 测试完成后点击"清理测试任务"
        </div>
    </div>

    <!-- 测试任务列表 -->
    <div class="card">
        <h2>📝 测试任务列表</h2>
        <?php
        try {
            $pdo = getDB();
            $stmt = $pdo->query("SELECT * FROM tasks WHERE name LIKE 'Cron测试任务%' ORDER BY created_at DESC");
            $testTasks = $stmt->fetchAll();
            
            if (empty($testTasks)) {
                echo "<p>暂无测试任务</p>";
            } else {
                echo "<table>";
                echo "<tr><th>任务名称</th><th>状态</th><th>执行次数</th><th>上次执行</th><th>创建时间</th></tr>";
                foreach ($testTasks as $task) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($task['name']) . "</td>";
                    echo "<td>" . ($task['status'] === 'enabled' ? '✅ 启用' : '⏸️ 禁用') . "</td>";
                    echo "<td>" . $task['run_count'] . "</td>";
                    echo "<td>" . ($task['last_run'] ?: '从未执行') . "</td>";
                    echo "<td>" . $task['created_at'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo '<form method="POST" style="margin-top: 15px;">';
                echo '<button type="submit" name="cleanup_test" class="btn-danger">';
                echo '🗑️ 清理所有测试任务';
                echo '</button>';
                echo '</form>';
            }
        } catch (Exception $e) {
            echo "<p>❌ 查询错误: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <!-- 执行日志 -->
    <div class="card">
        <h2>📄 测试任务执行日志</h2>
        <?php
        try {
            $pdo = getDB();
            $stmt = $pdo->query("
                SELECT l.*, t.name as task_name 
                FROM task_logs l 
                LEFT JOIN tasks t ON l.task_id = t.id 
                WHERE t.name LIKE 'Cron测试任务%' OR l.task_id IN (
                    SELECT id FROM tasks WHERE name LIKE 'Cron测试任务%'
                )
                ORDER BY l.start_time DESC 
                LIMIT 10
            ");
            $logs = $stmt->fetchAll();
            
            if (empty($logs)) {
                echo "<p>暂无执行日志</p>";
                echo "<div class='alert alert-warning'>";
                echo "如果您已经创建了测试任务但没有执行日志，可能的原因：<br>";
                echo "1. 系统Cron未正确设置<br>";
                echo "2. PHP命令执行函数被禁用<br>";
                echo "3. 任务刚创建，还未到执行时间";
                echo "</div>";
            } else {
                echo "<table>";
                echo "<tr><th>任务名称</th><th>状态</th><th>执行时间</th><th>耗时</th><th>输出</th></tr>";
                foreach ($logs as $log) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($log['task_name'] ?: '已删除任务') . "</td>";
                    echo "<td class='status-{$log['status']}'>" . ($log['status'] === 'success' ? '✅ 成功' : '❌ 失败') . "</td>";
                    echo "<td>" . $log['start_time'] . "</td>";
                    echo "<td>" . $log['duration'] . "秒</td>";
                    echo "<td>" . htmlspecialchars(substr($log['output'] ?: $log['error_message'] ?: '', 0, 50)) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                if (count($logs) > 0) {
                    echo "<div class='alert alert-success' style='margin-top: 15px;'>";
                    echo "🎉 太好了！发现执行日志，说明系统Cron正常工作！";
                    echo "</div>";
                }
            }
        } catch (Exception $e) {
            echo "<p>❌ 查询错误: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <!-- 手动测试 -->
    <div class="card">
        <h2>🔧 手动测试</h2>
        <p>如果自动测试有问题，可以手动测试Cron脚本：</p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <strong>命令行测试：</strong><br>
            <code>cd <?= dirname(__FILE__) ?></code><br>
            <code>php index.php cron</code>
        </div>
        
        <p>如果手动执行有输出且无错误，说明脚本本身没问题，问题可能在系统Cron设置上。</p>
    </div>

    <div class="card">
        <p><strong>相关链接：</strong></p>
        <p>
            <a href="index.php">返回主界面</a> | 
            <a href="debug.php">调试工具</a> | 
            <a href="setup_guide.php">设置指南</a>
        </p>
    </div>

    <script>
        // 自动刷新页面（可选）
        // setTimeout(() => location.reload(), 60000); // 60秒后刷新
    </script>
</body>
</html>
