#!/usr/bin/env php
<?php
/**
 * TaskMgr 安装脚本
 * 适用于openEuler 24操作系统
 */

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    echo "错误: 需要PHP 7.4或更高版本，当前版本: " . PHP_VERSION . "\n";
    exit(1);
}

// 检查必需的PHP扩展
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "错误: 缺少必需的PHP扩展: " . implode(', ', $missingExtensions) . "\n";
    echo "请安装缺少的扩展后重试\n";
    exit(1);
}

echo "TaskMgr 计划任务管理器安装程序\n";
echo "================================\n\n";

// 检查目录权限
$directories = ['logs', 'public'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    if (!is_writable($dir)) {
        echo "警告: 目录 $dir 不可写，请检查权限\n";
    }
}

// 数据库配置
echo "数据库配置\n";
echo "----------\n";

$dbConfig = [];

// 读取现有配置
$configFile = 'config/database.php';
if (file_exists($configFile)) {
    $existingConfig = require $configFile;
    echo "发现现有配置，按回车使用默认值\n\n";
} else {
    $existingConfig = [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'taskmgr',
        'username' => 'root',
        'password' => 'YC@yc110',
        'charset' => 'utf8mb4'
    ];
}

// 获取数据库配置
$dbConfig['host'] = readline("数据库主机 [{$existingConfig['host']}]: ") ?: $existingConfig['host'];
$dbConfig['port'] = (int)(readline("数据库端口 [{$existingConfig['port']}]: ") ?: $existingConfig['port']);
$dbConfig['database'] = readline("数据库名称 [{$existingConfig['database']}]: ") ?: $existingConfig['database'];
$dbConfig['username'] = readline("数据库用户名 [{$existingConfig['username']}]: ") ?: $existingConfig['username'];

// 密码输入
echo "数据库密码: ";
system('stty -echo');
$dbConfig['password'] = trim(fgets(STDIN));
system('stty echo');
echo "\n";

$dbConfig['charset'] = $existingConfig['charset'];
$dbConfig['options'] = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
];

// 测试数据库连接
echo "\n测试数据库连接...\n";

try {
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
    echo "数据库连接成功\n";
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 创建数据库（如果不存在）
try {
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbConfig['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "数据库创建成功\n";
} catch (PDOException $e) {
    echo "创建数据库失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 保存数据库配置
$configContent = "<?php\n/**\n * 数据库配置文件\n * 适用于openEuler 24操作系统\n */\n\nreturn " . var_export($dbConfig, true) . ";";

if (!file_put_contents($configFile, $configContent)) {
    echo "保存数据库配置失败\n";
    exit(1);
}

echo "数据库配置已保存\n";

// 执行数据库初始化
echo "\n初始化数据库表...\n";

try {
    // 连接到指定数据库
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
    
    // 读取并执行SQL文件
    $sqlFile = 'database/init.sql';
    if (!file_exists($sqlFile)) {
        echo "SQL初始化文件不存在: $sqlFile\n";
        exit(1);
    }
    
    $sql = file_get_contents($sqlFile);
    
    // 移除USE语句，因为我们已经连接到指定数据库
    $sql = preg_replace('/USE\s+\w+;\s*/i', '', $sql);
    
    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $pdo->exec($statement);
        }
    }
    
    echo "数据库表初始化成功\n";
    
} catch (PDOException $e) {
    echo "数据库初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 设置文件权限
echo "\n设置文件权限...\n";

$permissions = [
    'logs' => 0755,
    'config' => 0755,
    'config/database.php' => 0600,
    'public' => 0755,
    'src' => 0755
];

foreach ($permissions as $path => $perm) {
    if (file_exists($path)) {
        chmod($path, $perm);
        echo "设置 $path 权限为 " . decoct($perm) . "\n";
    }
}

// 创建cron任务
echo "\n配置计划任务...\n";

$cronCommand = "* * * * * /usr/bin/php " . __DIR__ . "/cron.php >> " . __DIR__ . "/logs/cron.log 2>&1";

echo "请将以下内容添加到crontab中:\n";
echo "crontab -e\n";
echo "$cronCommand\n\n";

echo "或者运行以下命令自动添加:\n";
echo "(crontab -l 2>/dev/null; echo \"$cronCommand\") | crontab -\n\n";

// 创建cron执行脚本
$cronScript = '#!/usr/bin/env php
<?php
/**
 * Cron执行脚本
 */

// 设置工作目录
chdir(__DIR__);

// 自动加载类文件
spl_autoload_register(function ($class) {
    $file = __DIR__ . \'/src/\' . $class . \'.php\';
    if (file_exists($file)) {
        require_once $file;
    }
});

try {
    $executor = new TaskExecutor();
    $task = new Task();
    $logger = new Logger();
    
    $tasksToRun = $task->getTasksToRun();
    
    foreach ($tasksToRun as $taskData) {
        try {
            $executor->executeTask($taskData[\'id\']);
        } catch (Exception $e) {
            $logger->error(\'Cron执行任务失败\', [
                \'task_id\' => $taskData[\'id\'],
                \'error\' => $e->getMessage()
            ]);
        }
    }
    
} catch (Exception $e) {
    error_log("TaskMgr Cron Error: " . $e->getMessage());
}
';

file_put_contents('cron.php', $cronScript);
chmod('cron.php', 0755);

echo "Cron执行脚本已创建: cron.php\n";

// 安装完成
echo "\n安装完成!\n";
echo "=========\n\n";

echo "访问地址: http://your-server/TaskMgr/public/\n";
echo "配置文件: config/database.php\n";
echo "日志目录: logs/\n";
echo "Cron脚本: cron.php\n\n";

echo "下一步:\n";
echo "1. 配置Web服务器指向 public 目录\n";
echo "2. 添加cron任务以启用自动执行\n";
echo "3. 访问Web界面开始使用\n\n";

echo "注意事项:\n";
echo "- 确保Web服务器有权限访问项目目录\n";
echo "- 确保logs目录可写\n";
echo "- 定期清理日志文件\n";
echo "- 谨慎配置任务命令，避免安全风险\n\n";

echo "感谢使用TaskMgr!\n";
