<?php
/**
 * 任务管理API接口
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 自动加载类文件
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/../../src/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 错误处理
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message], JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = 'success') {
    echo json_encode(['success' => true, 'message' => $message, 'data' => $data], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查请求频率限制
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!Security::checkRateLimit($clientIP, 100, 60)) {
    sendError('请求过于频繁，请稍后再试', 429);
}

try {
    $task = new Task();
    $method = $_SERVER['REQUEST_METHOD'];
    $pathInfo = $_SERVER['PATH_INFO'] ?? '';
    
    // 解析路径参数
    $pathParts = array_filter(explode('/', $pathInfo));
    $taskId = isset($pathParts[0]) ? (int)$pathParts[0] : null;
    $action = isset($pathParts[1]) ? $pathParts[1] : null;
    
    switch ($method) {
        case 'GET':
            if ($taskId) {
                // 获取单个任务
                $taskData = $task->getTaskById($taskId);
                if (!$taskData) {
                    sendError('任务不存在', 404);
                }
                sendSuccess($taskData);
                
            } elseif (isset($_GET['stats'])) {
                // 获取统计信息
                $stats = $task->getTaskStats();
                sendSuccess($stats);
                
            } else {
                // 获取任务列表
                $status = $_GET['status'] ?? null;
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : null;
                $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                
                $tasks = $task->getAllTasks($status, $limit, $offset);
                sendSuccess($tasks);
            }
            break;
            
        case 'POST':
            // 创建新任务
            $input = json_decode(file_get_contents('php://input'), true);

            if (!$input) {
                sendError('无效的JSON数据');
            }

            // 安全验证
            $errors = Security::validateTaskData($input);
            if (!empty($errors)) {
                Security::logSecurityEvent('Invalid task data', ['errors' => $errors]);
                sendError(implode(', ', $errors));
            }

            // 清理输入数据
            $input['name'] = Security::sanitizeInput($input['name']);
            $input['description'] = Security::sanitizeInput($input['description'] ?? '');
            $input['command'] = trim($input['command']); // 命令不进行HTML转义

            $taskId = $task->createTask($input);
            sendSuccess(['id' => $taskId], '任务创建成功');
            break;
            
        case 'PUT':
            if (!$taskId) {
                sendError('缺少任务ID');
            }
            
            if ($action === 'status') {
                // 更新任务状态
                $input = json_decode(file_get_contents('php://input'), true);
                $status = $input['status'] ?? '';
                
                $allowedStatuses = ['enabled', 'disabled'];
                if (!in_array($status, $allowedStatuses)) {
                    sendError('无效的状态值');
                }
                
                $result = $task->updateTaskStatus($taskId, $status);
                if ($result) {
                    sendSuccess(null, '状态更新成功');
                } else {
                    sendError('状态更新失败');
                }
                
            } else {
                // 更新任务信息
                $input = json_decode(file_get_contents('php://input'), true);

                if (!$input) {
                    sendError('无效的JSON数据');
                }

                // 安全验证（只验证提供的字段）
                $partialData = array_intersect_key($input, array_flip(['name', 'description', 'command', 'cron_expression', 'timeout_seconds', 'max_retries', 'status']));
                $errors = Security::validateTaskData($partialData);
                if (!empty($errors)) {
                    Security::logSecurityEvent('Invalid task update data', ['errors' => $errors]);
                    sendError(implode(', ', $errors));
                }

                // 清理输入数据
                if (isset($input['name'])) {
                    $input['name'] = Security::sanitizeInput($input['name']);
                }
                if (isset($input['description'])) {
                    $input['description'] = Security::sanitizeInput($input['description']);
                }

                $result = $task->updateTask($taskId, $input);
                if ($result) {
                    sendSuccess(null, '任务更新成功');
                } else {
                    sendError('任务更新失败');
                }
            }
            break;
            
        case 'DELETE':
            if (!$taskId) {
                sendError('缺少任务ID');
            }
            
            $result = $task->deleteTask($taskId);
            if ($result) {
                sendSuccess(null, '任务删除成功');
            } else {
                sendError('任务删除失败');
            }
            break;
            
        default:
            sendError('不支持的请求方法', 405);
    }
    
} catch (Exception $e) {
    $logger = new Logger();
    $logger->error('API错误', [
        'method' => $_SERVER['REQUEST_METHOD'],
        'uri' => $_SERVER['REQUEST_URI'],
        'error' => $e->getMessage()
    ]);
    
    sendError('服务器内部错误', 500);
}
