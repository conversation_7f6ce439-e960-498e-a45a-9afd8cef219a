#!/bin/bash
#
# TaskMgr 系统Cron设置脚本
# 用于设置系统级别的Cron任务来执行我们的计划任务
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INDEX_FILE="$SCRIPT_DIR/index.php"

echo "TaskMgr 系统Cron设置"
echo "==================="
echo ""

# 检查index.php文件
if [ ! -f "$INDEX_FILE" ]; then
    log_error "未找到index.php文件: $INDEX_FILE"
    exit 1
fi

log_info "TaskMgr路径: $SCRIPT_DIR"
log_info "主文件: $INDEX_FILE"

# 查找PHP可执行文件
PHP_BIN=""
for php_path in php /usr/bin/php /usr/local/bin/php /opt/php/bin/php; do
    if command -v "$php_path" >/dev/null 2>&1; then
        PHP_BIN="$php_path"
        break
    fi
done

if [ -z "$PHP_BIN" ]; then
    log_error "未找到PHP可执行文件"
    echo "请先安装PHP或确保PHP在系统PATH中"
    exit 1
fi

log_success "找到PHP: $PHP_BIN"

# 测试PHP脚本
log_info "测试TaskMgr脚本..."
if $PHP_BIN "$INDEX_FILE" cron 2>/dev/null; then
    log_success "TaskMgr脚本测试成功"
else
    log_warning "TaskMgr脚本测试可能有问题，但继续安装"
fi

# 检查当前crontab
log_info "检查当前crontab设置..."
CURRENT_CRON=$(crontab -l 2>/dev/null)

# 检查是否已经设置
if echo "$CURRENT_CRON" | grep -q "$INDEX_FILE"; then
    log_warning "TaskMgr Cron任务已经存在"
    echo "当前设置:"
    echo "$CURRENT_CRON" | grep "$INDEX_FILE"
    echo ""
    read -p "是否要重新设置？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消设置"
        exit 0
    fi
fi

# 创建新的crontab
log_info "设置系统Cron任务..."

# 创建临时文件
TEMP_CRON=$(mktemp)

# 获取现有crontab（排除已有的TaskMgr设置）
if [ -n "$CURRENT_CRON" ]; then
    echo "$CURRENT_CRON" | grep -v "$INDEX_FILE" > "$TEMP_CRON"
fi

# 添加TaskMgr Cron任务
echo "" >> "$TEMP_CRON"
echo "# TaskMgr 计划任务管理器 - 每分钟检查一次" >> "$TEMP_CRON"
echo "* * * * * $PHP_BIN $INDEX_FILE cron >/dev/null 2>&1" >> "$TEMP_CRON"

# 安装新的crontab
if crontab "$TEMP_CRON"; then
    log_success "系统Cron任务设置成功！"
    echo ""
    echo "设置详情:"
    echo "- 执行频率: 每分钟检查一次"
    echo "- PHP路径: $PHP_BIN"
    echo "- 脚本路径: $INDEX_FILE"
    echo "- 命令: $PHP_BIN $INDEX_FILE cron"
    echo ""
    log_info "现在您的定时任务将自动执行！"
else
    log_error "设置系统Cron任务失败"
    rm -f "$TEMP_CRON"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_CRON"

echo ""
log_info "验证设置..."
echo "当前crontab内容:"
echo "=================="
crontab -l | grep -A2 -B2 "TaskMgr"

echo ""
echo "🎉 设置完成！"
echo ""
echo "接下来："
echo "1. 您的定时任务现在会自动执行"
echo "2. 可以在Web界面查看执行日志"
echo "3. 如需取消，运行: crontab -e 然后删除TaskMgr相关行"
echo ""
echo "测试建议："
echo "1. 创建一个每分钟执行的测试任务"
echo "2. 等待1-2分钟查看是否执行"
echo "3. 检查执行日志确认正常工作"
