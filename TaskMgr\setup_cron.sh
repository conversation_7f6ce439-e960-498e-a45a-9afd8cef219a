#!/bin/bash
#
# TaskMgr 系统Cron设置脚本
# 用于设置系统级别的Cron任务来执行我们的计划任务
#

# ============================================
# 配置区域 - 根据服务器环境修改以下变量
# ============================================

# PHP可执行文件路径 - 根据服务器环境修改
# 常见路径示例：
# - 宝塔面板: /www/server/php/80/bin/php (80可以是其他版本号)
# - 普通Linux: /usr/bin/php
# - 编译安装: /usr/local/bin/php
PHP_PATH="/www/server/php/80/bin/php"

# 是否启用自动搜索PHP路径（如果上面的路径无效）
# true: 启用自动搜索  false: 只使用上面指定的路径
AUTO_SEARCH_PHP=true

# ============================================
# 脚本开始 - 一般不需要修改下面的内容
# ============================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INDEX_FILE="$SCRIPT_DIR/index.php"

echo "TaskMgr 系统Cron设置"
echo "==================="
echo ""

# 检查index.php文件
if [ ! -f "$INDEX_FILE" ]; then
    log_error "未找到index.php文件: $INDEX_FILE"
    exit 1
fi

log_info "TaskMgr路径: $SCRIPT_DIR"
log_info "主文件: $INDEX_FILE"

# 查找PHP可执行文件
PHP_BIN=""

log_info "检查PHP可执行文件..."

# 方法1: 优先使用配置的PHP路径
log_info "检查配置的PHP路径: $PHP_PATH"
if [ -x "$PHP_PATH" ]; then
    version=$($PHP_PATH -v 2>/dev/null | head -n1)
    if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
        log_success "使用配置的PHP路径: $PHP_PATH"
        log_info "版本: $version"
        PHP_BIN="$PHP_PATH"
    else
        log_warning "配置的PHP路径无效或无法执行"
    fi
else
    log_warning "配置的PHP路径不存在: $PHP_PATH"
fi

# 方法2: 如果配置路径无效且启用自动搜索，则使用which命令查找
if [ -z "$PHP_BIN" ] && [ "$AUTO_SEARCH_PHP" = true ]; then
    log_info "启用自动搜索PHP..."
for cmd in php php8 php7 php81 php80 php74 php73; do
    FOUND_PATH=$(which $cmd 2>/dev/null)
    if [ -n "$FOUND_PATH" ] && [ -x "$FOUND_PATH" ]; then
        version=$($FOUND_PATH -v 2>/dev/null | head -n1)
        if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
            log_success "找到: $FOUND_PATH"
            log_info "版本: $version"
            PHP_BIN="$FOUND_PATH"
            break
        fi
    fi
done

# 方法2: 如果which没找到，尝试whereis
if [ -z "$PHP_BIN" ]; then
    log_info "使用whereis命令搜索..."
    WHEREIS_OUTPUT=$(whereis php 2>/dev/null)
    if [ -n "$WHEREIS_OUTPUT" ]; then
        log_info "whereis结果: $WHEREIS_OUTPUT"
        # 提取路径
        for path in $WHEREIS_OUTPUT; do
            if [[ "$path" == *"/php"* ]] && [ -x "$path" ] 2>/dev/null; then
                version=$($path -v 2>/dev/null | head -n1)
                if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
                    log_success "找到: $path"
                    log_info "版本: $version"
                    PHP_BIN="$path"
                    break
                fi
            fi
        done
    fi
fi

# 方法3: 尝试常见路径
if [ -z "$PHP_BIN" ]; then
    log_info "检查常见路径..."
    COMMON_PATHS=(
        "/usr/bin/php"
        "/usr/local/bin/php"
        "/opt/php/bin/php"
        "/usr/bin/php8"
        "/usr/bin/php7"
    )

    for path in "${COMMON_PATHS[@]}"; do
        if [ -x "$path" ] 2>/dev/null; then
            version=$($path -v 2>/dev/null | head -n1)
            if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
                log_success "找到: $path"
                log_info "版本: $version"
                PHP_BIN="$path"
                break
            fi
        fi
    done
fi

# 方法4: 使用find命令搜索（最后的尝试）
if [ -z "$PHP_BIN" ]; then
    log_info "使用find命令搜索（可能需要一些时间）..."
    FOUND_PHP=$(find /usr /opt 2>/dev/null -name "php" -type f -executable | head -1)
    if [ -n "$FOUND_PHP" ] && [ -x "$FOUND_PHP" ]; then
        version=$($FOUND_PHP -v 2>/dev/null | head -n1)
        if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
            log_success "通过find找到: $FOUND_PHP"
            log_info "版本: $version"
            PHP_BIN="$FOUND_PHP"
        fi
    fi
fi

if [ -z "$PHP_BIN" ]; then
    log_error "未找到PHP可执行文件"
    echo ""
    echo "请尝试以下方法："
    echo "1. 检查PHP是否已安装: php -v"
    echo "2. 查找PHP路径: which php 或 whereis php"
    echo "3. 手动指定PHP路径，编辑此脚本第125行左右的PHP_BIN变量"
    echo ""
    read -p "是否要手动输入PHP路径？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "请输入PHP可执行文件的完整路径："
        read -r MANUAL_PHP_PATH
        if [ -n "$MANUAL_PHP_PATH" ] && [ -x "$MANUAL_PHP_PATH" ]; then
            version=$($MANUAL_PHP_PATH -v 2>/dev/null | head -n1)
            if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
                PHP_BIN="$MANUAL_PHP_PATH"
                log_success "手动设置PHP路径: $PHP_BIN"
                log_info "版本: $version"
            else
                log_error "指定的路径不是有效的PHP可执行文件"
                exit 1
            fi
        else
            log_error "指定的路径无效或不可执行"
            exit 1
        fi
    else
        exit 1
    fi
fi

log_success "使用PHP: $PHP_BIN"

# 测试PHP脚本
log_info "测试TaskMgr脚本..."
echo "执行命令: $PHP_BIN $INDEX_FILE cron"
TEST_OUTPUT=$($PHP_BIN "$INDEX_FILE" cron 2>&1)
TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_success "TaskMgr脚本测试成功"
    if [ -n "$TEST_OUTPUT" ]; then
        echo "输出: $TEST_OUTPUT"
    fi
else
    log_warning "TaskMgr脚本测试有问题，退出码: $TEST_EXIT_CODE"
    if [ -n "$TEST_OUTPUT" ]; then
        echo "错误输出: $TEST_OUTPUT"
    fi
    echo ""
    read -p "是否继续安装Cron？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 1
    fi
fi

# 检查当前crontab
log_info "检查当前crontab设置..."
CURRENT_CRON=$(crontab -l 2>/dev/null)

# 检查是否已经设置
if echo "$CURRENT_CRON" | grep -q "$INDEX_FILE"; then
    log_warning "TaskMgr Cron任务已经存在"
    echo "当前设置:"
    echo "$CURRENT_CRON" | grep "$INDEX_FILE"
    echo ""
    read -p "是否要重新设置？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消设置"
        exit 0
    fi
fi

# 创建新的crontab
log_info "设置系统Cron任务..."

# 创建临时文件
TEMP_CRON=$(mktemp)

# 获取现有crontab（排除已有的TaskMgr设置）
if [ -n "$CURRENT_CRON" ]; then
    echo "$CURRENT_CRON" | grep -v "$INDEX_FILE" > "$TEMP_CRON"
fi

# 添加TaskMgr Cron任务
echo "" >> "$TEMP_CRON"
echo "# TaskMgr 计划任务管理器 - 每分钟检查一次" >> "$TEMP_CRON"
echo "* * * * * $PHP_BIN $INDEX_FILE cron >/dev/null 2>&1" >> "$TEMP_CRON"

# 安装新的crontab
if crontab "$TEMP_CRON"; then
    log_success "系统Cron任务设置成功！"
    echo ""
    echo "设置详情:"
    echo "- 执行频率: 每分钟检查一次"
    echo "- PHP路径: $PHP_BIN"
    echo "- 脚本路径: $INDEX_FILE"
    echo "- 命令: $PHP_BIN $INDEX_FILE cron"
    echo ""
    log_info "现在您的定时任务将自动执行！"
else
    log_error "设置系统Cron任务失败"
    rm -f "$TEMP_CRON"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_CRON"

echo ""
log_info "验证设置..."
echo "当前crontab内容:"
echo "=================="
crontab -l | grep -A2 -B2 "TaskMgr"

echo ""
echo "🎉 设置完成！"
echo ""
echo "重要信息："
echo "- PHP路径: $PHP_BIN"
echo "- TaskMgr路径: $INDEX_FILE"
echo "- Cron命令: $PHP_BIN $INDEX_FILE cron"
echo ""
echo "接下来："
echo "1. 您的定时任务现在会自动执行（每分钟检查一次）"
echo "2. 访问Web界面查看执行日志: http://your-server/TaskMgr/"
echo "3. 如需取消，运行: crontab -e 然后删除TaskMgr相关行"
echo ""
echo "验证方法："
echo "1. 访问: http://your-server/TaskMgr/test_cron.php"
echo "2. 创建一个测试任务"
echo "3. 等待1-2分钟查看是否自动执行"
echo "4. 检查执行日志确认正常工作"
echo ""
echo "故障排除："
echo "- 如果任务不执行，检查: tail -f /var/log/cron"
echo "- 手动测试命令: $PHP_BIN $INDEX_FILE cron"
echo "- 查看诊断信息: http://your-server/TaskMgr/diagnose.php"
