<?php
/**
 * TaskMgr 问题诊断工具
 * 详细检查系统配置和可能的问题
 */

// 检查是否通过Web访问
$isWeb = !empty($_SERVER['HTTP_HOST']);

if ($isWeb) {
    // Web访问
    header('Content-Type: text/html; charset=utf-8');
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskMgr 问题诊断</title>
    <style>
        body { font-family: monospace; background: #f5f5f5; padding: 20px; }
        .container { background: white; padding: 20px; border-radius: 8px; max-width: 1000px; margin: 0 auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .section { margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
    </style>
</head>
<body>
<div class="container">
<h1>TaskMgr 问题诊断工具</h1>
<p><a href="index.php">← 返回主界面</a> | <a href="test_cron.php">Cron测试</a> | <a href="debug.php">调试工具</a></p>
<hr>';

    function output($text, $class = '') {
        if ($class) {
            echo "<span class='$class'>$text</span>";
        } else {
            echo htmlspecialchars($text);
        }
    }

    function section($title) {
        echo "<div class='section'><h2>$title</h2>";
    }

    function endSection() {
        echo "</div>";
    }

} else {
    // 命令行访问
    function output($text, $class = '') {
        echo $text;
    }

    function section($title) {
        echo "\n$title\n" . str_repeat('-', strlen($title)) . "\n";
    }

    function endSection() {
        echo "\n";
    }
}

section("1. 基本信息检查");

output("当前时间: " . date('Y-m-d H:i:s') . "\n");
output("时区设置: " . date_default_timezone_get() . "\n");
output("Unix时间戳: " . time() . "\n");
output("服务器时间: " . $_SERVER['REQUEST_TIME'] . "\n");
endSection();

section("2. 文件路径检查");
$currentDir = __DIR__;
$indexFile = $currentDir . '/index.php';
output("当前目录: $currentDir\n");
output("index.php: " . (file_exists($indexFile) ? "✓ 存在" : "✗ 不存在"), file_exists($indexFile) ? 'success' : 'error');
output("\nindex.php路径: $indexFile\n");
output("Web访问路径: " . ($_SERVER['HTTP_HOST'] ?? 'N/A') . $_SERVER['REQUEST_URI'] ?? '' . "\n");
endSection();

section("3. PHP环境检查");
output("PHP版本: " . PHP_VERSION . "\n");
output("PHP SAPI: " . php_sapi_name() . "\n");
output("PHP可执行文件: " . (PHP_BINARY ?: '未知') . "\n");

// 检查命令执行函数
$functions = ['exec', 'shell_exec', 'system', 'passthru'];
output("命令执行函数:\n");
$availableFunctions = 0;
foreach ($functions as $func) {
    $available = function_exists($func);
    if ($available) $availableFunctions++;
    $status = $available ? "✓ 可用" : "✗ 禁用";
    $class = $available ? 'success' : 'error';
    output("  $func: ", '');
    output($status, $class);
    output("\n");
}

if ($availableFunctions == 0) {
    output("⚠️ 警告: 所有命令执行函数都被禁用，Cron任务无法执行！\n", 'error');
}
endSection();

// 4. 检查系统Cron设置
echo "4. 系统Cron检查\n";
echo "-------------\n";
$cronList = shell_exec('crontab -l 2>/dev/null');
if ($cronList) {
    echo "当前crontab内容:\n";
    echo $cronList . "\n";
    
    // 检查是否包含TaskMgr
    if (strpos($cronList, 'index.php') !== false) {
        echo "✓ 发现TaskMgr相关的Cron设置\n";
        
        // 提取TaskMgr的Cron行
        $lines = explode("\n", $cronList);
        foreach ($lines as $line) {
            if (strpos($line, 'index.php') !== false && !preg_match('/^\s*#/', $line)) {
                echo "TaskMgr Cron行: $line\n";
            }
        }
    } else {
        echo "✗ 未发现TaskMgr相关的Cron设置\n";
    }
} else {
    echo "✗ 未找到crontab设置或crontab为空\n";
}
echo "\n";

// 5. 测试Cron脚本执行
echo "5. Cron脚本测试\n";
echo "-------------\n";
if (file_exists($indexFile)) {
    echo "测试执行: php $indexFile cron\n";
    
    $output = '';
    $returnCode = 0;
    
    if (function_exists('exec')) {
        exec("php $indexFile cron 2>&1", $outputLines, $returnCode);
        $output = implode("\n", $outputLines);
    } elseif (function_exists('shell_exec')) {
        $output = shell_exec("php $indexFile cron 2>&1");
        $returnCode = 0; // shell_exec无法获取返回码
    } else {
        $output = "错误: 所有命令执行函数都被禁用";
        $returnCode = -1;
    }
    
    echo "返回码: $returnCode\n";
    echo "输出内容:\n";
    echo $output ? $output : "(无输出)\n";
} else {
    echo "✗ index.php文件不存在，无法测试\n";
}
echo "\n";

// 6. 检查数据库连接和任务
echo "6. 数据库检查\n";
echo "----------\n";
try {
    // 数据库配置
    $db_config = [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'taskmgr',
        'username' => 'root',
        'password' => 'YC@yc110',
        'charset' => 'utf8mb4'
    ];
    
    $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['database']};charset={$db_config['charset']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✓ 数据库连接成功\n";
    
    // 检查任务表
    $taskCount = $pdo->query("SELECT COUNT(*) FROM tasks")->fetchColumn();
    echo "任务总数: $taskCount\n";
    
    $enabledCount = $pdo->query("SELECT COUNT(*) FROM tasks WHERE status = 'enabled'")->fetchColumn();
    echo "启用任务数: $enabledCount\n";
    
    // 检查当前应该执行的任务
    echo "\n当前时间应该执行的任务:\n";
    $currentMinute = (int)date('i');
    $currentHour = (int)date('G');
    $currentDay = (int)date('j');
    $currentMonth = (int)date('n');
    $currentWeekday = (int)date('w');
    
    echo "当前时间参数: 分钟=$currentMinute, 小时=$currentHour, 日=$currentDay, 月=$currentMonth, 周=$currentWeekday\n";
    
    $tasks = $pdo->query("SELECT * FROM tasks WHERE status = 'enabled'")->fetchAll();
    foreach ($tasks as $task) {
        echo "\n任务ID {$task['id']}: {$task['name']}\n";
        echo "  Cron表达式: {$task['cron_expression']}\n";
        echo "  上次执行: " . ($task['last_run'] ?: '从未执行') . "\n";
        echo "  执行次数: {$task['run_count']}\n";
        
        // 简单的Cron匹配检查
        $cronParts = explode(' ', $task['cron_expression']);
        if (count($cronParts) == 5) {
            list($minute, $hour, $day, $month, $weekday) = $cronParts;
            
            $minuteMatch = ($minute === '*' || $minute === (string)$currentMinute);
            $hourMatch = ($hour === '*' || $hour === (string)$currentHour);
            $dayMatch = ($day === '*' || $day === (string)$currentDay);
            $monthMatch = ($month === '*' || $month === (string)$currentMonth);
            $weekdayMatch = ($weekday === '*' || $weekday === (string)$currentWeekday);
            
            echo "  匹配检查: 分钟=" . ($minuteMatch ? '✓' : '✗') . 
                 " 小时=" . ($hourMatch ? '✓' : '✗') . 
                 " 日=" . ($dayMatch ? '✓' : '✗') . 
                 " 月=" . ($monthMatch ? '✓' : '✗') . 
                 " 周=" . ($weekdayMatch ? '✓' : '✗') . "\n";
            
            if ($minuteMatch && $hourMatch && $dayMatch && $monthMatch && $weekdayMatch) {
                echo "  *** 这个任务现在应该执行！***\n";
            }
        }
    }
    
    // 检查最近的执行日志
    echo "\n最近的执行日志:\n";
    $logs = $pdo->query("SELECT * FROM task_logs ORDER BY start_time DESC LIMIT 5")->fetchAll();
    if (empty($logs)) {
        echo "  无执行日志\n";
    } else {
        foreach ($logs as $log) {
            echo "  {$log['start_time']}: 任务{$log['task_id']} - {$log['status']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 7. 系统信息
echo "7. 系统信息\n";
echo "--------\n";
echo "操作系统: " . PHP_OS . "\n";
echo "当前用户: " . get_current_user() . "\n";
echo "进程ID: " . getmypid() . "\n";

// 检查Cron服务状态
echo "\nCron服务状态:\n";
$cronStatus = shell_exec('systemctl is-active cron 2>/dev/null || systemctl is-active crond 2>/dev/null');
if ($cronStatus) {
    echo "Cron服务: " . trim($cronStatus) . "\n";
} else {
    echo "无法检查Cron服务状态\n";
}

echo "\n";

// 8. 建议和解决方案
echo "8. 问题诊断和建议\n";
echo "---------------\n";

$issues = [];
$suggestions = [];

// 检查各种可能的问题
if (!$cronList || strpos($cronList, 'index.php') === false) {
    $issues[] = "系统Cron未正确设置";
    $suggestions[] = "重新运行: ./setup_cron.sh";
}

if (!function_exists('exec') && !function_exists('shell_exec')) {
    $issues[] = "PHP命令执行函数被禁用";
    $suggestions[] = "参考setup_guide.php启用命令执行函数";
}

if (isset($returnCode) && $returnCode !== 0) {
    $issues[] = "Cron脚本执行失败";
    $suggestions[] = "检查PHP路径和文件权限";
}

if (isset($enabledCount) && $enabledCount == 0) {
    $issues[] = "没有启用的任务";
    $suggestions[] = "确保任务状态为'启用'";
}

if (empty($issues)) {
    echo "✓ 未发现明显问题，系统配置看起来正常\n";
    echo "\n可能的原因:\n";
    echo "- Cron刚刚设置，还未到下一个执行周期\n";
    echo "- 任务的Cron表达式可能不匹配当前时间\n";
    echo "- 系统时间可能有偏差\n";
} else {
    echo "发现以下问题:\n";
    foreach ($issues as $i => $issue) {
        echo ($i + 1) . ". $issue\n";
    }
    
    echo "\n建议解决方案:\n";
    foreach ($suggestions as $i => $suggestion) {
        echo ($i + 1) . ". $suggestion\n";
    }
}

echo "\n";
echo "如需进一步帮助，请:\n";
echo "1. 访问 test_cron.php 创建测试任务\n";
echo "2. 查看 debug.php 获取更多调试信息\n";
echo "3. 检查系统日志: tail -f /var/log/cron\n";
echo "\n";
?>
