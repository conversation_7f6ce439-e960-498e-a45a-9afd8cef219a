<?php
/**
 * 任务执行器
 */

class TaskExecutor {
    private $db;
    private $logger;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logger = new Logger();
        $this->config = require __DIR__ . '/../config/app.php';
    }
    
    /**
     * 执行单个任务
     */
    public function executeTask($taskId) {
        $task = new Task();
        $taskData = $task->getTaskById($taskId);
        
        if (!$taskData) {
            throw new Exception("任务不存在: $taskId");
        }
        
        if ($taskData['status'] !== 'enabled') {
            throw new Exception("任务未启用: $taskId");
        }
        
        // 检查任务锁
        if ($this->isTaskLocked($taskId)) {
            throw new Exception("任务正在执行中: $taskId");
        }
        
        // 创建任务锁
        $this->lockTask($taskId);
        
        $executionId = $this->generateExecutionId();
        
        try {
            // 更新任务状态为运行中
            $task->updateTaskStatus($taskId, 'running');
            
            // 记录开始执行
            $logId = $this->logTaskStart($taskId, $executionId);
            
            // 执行命令
            $result = $this->executeCommand($taskData['command'], $taskData['timeout_seconds']);
            
            // 记录执行结果
            $this->logTaskEnd($logId, $result);
            
            // 更新任务统计
            $task->updateTaskStats($taskId, $result['success']);
            
            // 更新任务状态
            $task->updateTaskStatus($taskId, $result['success'] ? 'completed' : 'failed');
            
            // 更新下次执行时间
            $task->updateNextRunTime($taskId);
            
            $this->logger->info("任务执行完成", [
                'task_id' => $taskId,
                'execution_id' => $executionId,
                'success' => $result['success']
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            // 记录错误
            if (isset($logId)) {
                $this->logTaskError($logId, $e->getMessage());
            }
            
            // 更新任务统计
            $task->updateTaskStats($taskId, false);
            
            // 更新任务状态
            $task->updateTaskStatus($taskId, 'failed');
            
            $this->logger->error("任务执行失败", [
                'task_id' => $taskId,
                'execution_id' => $executionId,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
            
        } finally {
            // 释放任务锁
            $this->unlockTask($taskId);
        }
    }
    
    /**
     * 执行命令
     */
    private function executeCommand($command, $timeout = 300) {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $descriptorspec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];
        
        $process = proc_open($command, $descriptorspec, $pipes);
        
        if (!is_resource($process)) {
            throw new Exception("无法启动进程");
        }
        
        // 关闭stdin
        fclose($pipes[0]);
        
        // 设置非阻塞模式
        stream_set_blocking($pipes[1], false);
        stream_set_blocking($pipes[2], false);
        
        $output = '';
        $error = '';
        $startTime = time();
        
        while (true) {
            $status = proc_get_status($process);
            
            // 检查超时
            if (time() - $startTime > $timeout) {
                proc_terminate($process, 9); // SIGKILL
                throw new Exception("任务执行超时");
            }
            
            // 读取输出
            $output .= stream_get_contents($pipes[1]);
            $error .= stream_get_contents($pipes[2]);
            
            // 检查进程是否结束
            if (!$status['running']) {
                break;
            }
            
            usleep(100000); // 0.1秒
        }
        
        // 读取剩余输出
        $output .= stream_get_contents($pipes[1]);
        $error .= stream_get_contents($pipes[2]);
        
        fclose($pipes[1]);
        fclose($pipes[2]);
        
        $exitCode = proc_close($process);
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        return [
            'success' => $exitCode === 0,
            'exit_code' => $exitCode,
            'output' => $output,
            'error' => $error,
            'duration' => round($endTime - $startTime, 2),
            'memory_usage' => round(($endMemory - $startMemory) / 1024, 2) // KB
        ];
    }
    
    /**
     * 检查任务是否被锁定
     */
    private function isTaskLocked($taskId) {
        $sql = "SELECT COUNT(*) as count FROM taskmgr_task_locks WHERE task_id = ? AND expires_at > NOW()";
        $result = $this->db->fetch($sql, [$taskId]);
        return $result['count'] > 0;
    }
    
    /**
     * 锁定任务
     */
    private function lockTask($taskId) {
        $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1小时后过期
        $lockedBy = getmypid(); // 进程ID
        
        $sql = "INSERT INTO taskmgr_task_locks (task_id, locked_by, expires_at) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE locked_by = ?, expires_at = ?";
        
        $this->db->execute($sql, [$taskId, $lockedBy, $expiresAt, $lockedBy, $expiresAt]);
    }
    
    /**
     * 解锁任务
     */
    private function unlockTask($taskId) {
        $sql = "DELETE FROM taskmgr_task_locks WHERE task_id = ?";
        $this->db->execute($sql, [$taskId]);
    }
    
    /**
     * 生成执行ID
     */
    private function generateExecutionId() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * 记录任务开始执行
     */
    private function logTaskStart($taskId, $executionId) {
        $sql = "INSERT INTO taskmgr_task_logs (task_id, execution_id, status, start_time) 
                VALUES (?, ?, 'running', NOW())";
        
        return $this->db->insert($sql, [$taskId, $executionId]);
    }
    
    /**
     * 记录任务执行结束
     */
    private function logTaskEnd($logId, $result) {
        $status = $result['success'] ? 'success' : 'failed';
        
        $sql = "UPDATE taskmgr_task_logs SET 
                status = ?, 
                end_time = NOW(), 
                duration = ?, 
                output = ?, 
                error_message = ?, 
                exit_code = ?, 
                memory_usage = ?
                WHERE id = ?";
        
        $this->db->execute($sql, [
            $status,
            $result['duration'],
            $result['output'],
            $result['error'],
            $result['exit_code'],
            $result['memory_usage'],
            $logId
        ]);
    }
    
    /**
     * 记录任务执行错误
     */
    private function logTaskError($logId, $errorMessage) {
        $sql = "UPDATE taskmgr_task_logs SET 
                status = 'failed', 
                end_time = NOW(), 
                error_message = ?
                WHERE id = ?";
        
        $this->db->execute($sql, [$errorMessage, $logId]);
    }
}
