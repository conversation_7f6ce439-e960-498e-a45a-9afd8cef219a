<?php
/**
 * 任务执行API接口
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 自动加载类文件
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/../../src/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 错误处理
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message], JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = 'success') {
    echo json_encode(['success' => true, 'message' => $message, 'data' => $data], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $pathInfo = $_SERVER['PATH_INFO'] ?? '';
    
    if ($method !== 'POST') {
        sendError('只支持POST请求', 405);
    }
    
    // 解析路径参数
    $pathParts = array_filter(explode('/', $pathInfo));
    $action = isset($pathParts[0]) ? $pathParts[0] : 'manual';
    
    $executor = new TaskExecutor();
    $logger = new Logger();
    
    switch ($action) {
        case 'manual':
            // 手动执行任务
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['task_id'])) {
                sendError('缺少task_id参数');
            }
            
            $taskId = (int)$input['task_id'];
            
            try {
                $result = $executor->executeTask($taskId);
                
                $logger->info('手动执行任务', [
                    'task_id' => $taskId,
                    'success' => $result['success'],
                    'duration' => $result['duration']
                ]);
                
                sendSuccess($result, '任务执行完成');
                
            } catch (Exception $e) {
                $logger->error('手动执行任务失败', [
                    'task_id' => $taskId,
                    'error' => $e->getMessage()
                ]);
                
                sendError($e->getMessage());
            }
            break;
            
        case 'scheduled':
            // 执行计划任务（由cron调用）
            $task = new Task();
            $tasksToRun = $task->getTasksToRun();
            
            $results = [];
            $successCount = 0;
            $failCount = 0;
            
            foreach ($tasksToRun as $taskData) {
                try {
                    $result = $executor->executeTask($taskData['id']);
                    
                    $results[] = [
                        'task_id' => $taskData['id'],
                        'task_name' => $taskData['name'],
                        'success' => $result['success'],
                        'duration' => $result['duration'],
                        'output' => substr($result['output'], 0, 500) // 限制输出长度
                    ];
                    
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                    
                } catch (Exception $e) {
                    $results[] = [
                        'task_id' => $taskData['id'],
                        'task_name' => $taskData['name'],
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                    
                    $failCount++;
                }
            }
            
            $logger->info('计划任务执行完成', [
                'total_tasks' => count($tasksToRun),
                'success_count' => $successCount,
                'fail_count' => $failCount
            ]);
            
            sendSuccess([
                'total_tasks' => count($tasksToRun),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ], '计划任务执行完成');
            break;
            
        case 'test':
            // 测试命令执行
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['command'])) {
                sendError('缺少command参数');
            }
            
            $command = $input['command'];
            $timeout = $input['timeout'] ?? 30;
            
            // 安全检查：限制可执行的命令
            $allowedCommands = [
                'echo', 'date', 'whoami', 'pwd', 'ls', 'df', 'free', 'uptime'
            ];
            
            $firstWord = explode(' ', trim($command))[0];
            if (!in_array($firstWord, $allowedCommands)) {
                sendError('不允许执行此命令，仅支持: ' . implode(', ', $allowedCommands));
            }
            
            try {
                // 使用反射调用私有方法进行测试
                $reflection = new ReflectionClass($executor);
                $method = $reflection->getMethod('executeCommand');
                $method->setAccessible(true);
                
                $result = $method->invoke($executor, $command, $timeout);
                
                $logger->info('测试命令执行', [
                    'command' => $command,
                    'success' => $result['success'],
                    'duration' => $result['duration']
                ]);
                
                sendSuccess($result, '命令测试完成');
                
            } catch (Exception $e) {
                $logger->error('测试命令执行失败', [
                    'command' => $command,
                    'error' => $e->getMessage()
                ]);
                
                sendError($e->getMessage());
            }
            break;
            
        case 'validate-cron':
            // 验证Cron表达式
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['cron_expression'])) {
                sendError('缺少cron_expression参数');
            }
            
            $cronExpression = $input['cron_expression'];
            $cronParser = new CronParser();
            
            $isValid = $cronParser->isValidCronExpression($cronExpression);
            
            if ($isValid) {
                $nextRunTime = $cronParser->getNextRunTime($cronExpression);
                $description = $cronParser->getDescription($cronExpression);
                
                sendSuccess([
                    'valid' => true,
                    'next_run_time' => $nextRunTime,
                    'description' => $description
                ], 'Cron表达式有效');
            } else {
                sendSuccess([
                    'valid' => false
                ], 'Cron表达式无效');
            }
            break;
            
        case 'cron-examples':
            // 获取Cron表达式示例
            $examples = CronParser::getExamples();
            sendSuccess($examples);
            break;
            
        default:
            sendError('无效的操作', 404);
    }
    
} catch (Exception $e) {
    $logger = new Logger();
    $logger->error('执行API错误', [
        'method' => $_SERVER['REQUEST_METHOD'],
        'uri' => $_SERVER['REQUEST_URI'],
        'error' => $e->getMessage()
    ]);
    
    sendError('服务器内部错误', 500);
}
