<?php
/**
 * 安全防护类
 */

class Security {
    
    /**
     * 防止SQL注入 - 验证和清理输入
     */
    public static function sanitizeInput($input, $type = 'string') {
        if ($input === null) {
            return null;
        }
        
        switch ($type) {
            case 'int':
                return filter_var($input, FILTER_VALIDATE_INT);
                
            case 'float':
                return filter_var($input, FILTER_VALIDATE_FLOAT);
                
            case 'email':
                return filter_var($input, FILTER_VALIDATE_EMAIL);
                
            case 'url':
                return filter_var($input, FILTER_VALIDATE_URL);
                
            case 'string':
            default:
                // 移除HTML标签和特殊字符
                $input = strip_tags($input);
                $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
                return trim($input);
        }
    }
    
    /**
     * 验证Cron表达式安全性
     */
    public static function validateCronExpression($cronExpression) {
        // 基本格式验证
        if (!preg_match('/^[\d\*\/\-\,\s]+$/', $cronExpression)) {
            return false;
        }
        
        $parts = explode(' ', trim($cronExpression));
        if (count($parts) !== 5) {
            return false;
        }
        
        // 验证每个字段
        $ranges = [
            [0, 59], // 分钟
            [0, 23], // 小时
            [1, 31], // 日期
            [1, 12], // 月份
            [0, 7]   // 星期
        ];
        
        foreach ($parts as $index => $part) {
            if (!self::validateCronField($part, $ranges[$index][0], $ranges[$index][1])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证单个Cron字段
     */
    private static function validateCronField($field, $min, $max) {
        // 通配符
        if ($field === '*') {
            return true;
        }
        
        // 步长值
        if (strpos($field, '/') !== false) {
            $parts = explode('/', $field);
            if (count($parts) !== 2) {
                return false;
            }
            
            $base = $parts[0];
            $step = $parts[1];
            
            if ($base !== '*' && !self::validateCronField($base, $min, $max)) {
                return false;
            }
            
            return is_numeric($step) && (int)$step > 0 && (int)$step <= $max;
        }
        
        // 范围
        if (strpos($field, '-') !== false) {
            $parts = explode('-', $field);
            if (count($parts) !== 2) {
                return false;
            }
            
            $start = (int)$parts[0];
            $end = (int)$parts[1];
            
            return $start >= $min && $end <= $max && $start <= $end;
        }
        
        // 列表
        if (strpos($field, ',') !== false) {
            $values = explode(',', $field);
            foreach ($values as $value) {
                if (!is_numeric($value) || (int)$value < $min || (int)$value > $max) {
                    return false;
                }
            }
            return true;
        }
        
        // 单个值
        return is_numeric($field) && (int)$field >= $min && (int)$field <= $max;
    }
    
    /**
     * 验证命令安全性
     */
    public static function validateCommand($command) {
        // 禁止的危险命令
        $dangerousCommands = [
            'rm', 'rmdir', 'del', 'format', 'fdisk',
            'mkfs', 'dd', 'shutdown', 'reboot', 'halt',
            'su', 'sudo', 'passwd', 'chown', 'chmod',
            'mount', 'umount', 'kill', 'killall',
            'nc', 'netcat', 'telnet', 'ssh', 'ftp',
            'wget', 'curl', 'lynx', 'links',
            'python', 'perl', 'ruby', 'php', 'node',
            'gcc', 'g++', 'make', 'cmake',
            'crontab', 'at', 'batch'
        ];
        
        // 检查命令的第一个词
        $firstWord = strtolower(explode(' ', trim($command))[0]);
        
        if (in_array($firstWord, $dangerousCommands)) {
            return false;
        }
        
        // 检查危险字符和模式
        $dangerousPatterns = [
            '/[;&|`$(){}]/',  // 命令分隔符和变量替换
            '/\.\.\//',       // 目录遍历
            '/\/etc\//',      // 系统配置目录
            '/\/proc\//',     // 进程信息目录
            '/\/sys\//',      // 系统信息目录
            '/\/dev\//',      // 设备目录
            '/\/root\//',     // root用户目录
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $command)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 生成CSRF令牌
     */
    public static function generateCSRFToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        
        return $token;
    }
    
    /**
     * 验证CSRF令牌
     */
    public static function validateCSRFToken($token) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION['csrf_token']) && 
               hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * 验证输入数据
     */
    public static function validateTaskData($data) {
        $errors = [];
        
        // 验证任务名称
        if (empty($data['name'])) {
            $errors[] = '任务名称不能为空';
        } elseif (strlen($data['name']) > 255) {
            $errors[] = '任务名称长度不能超过255个字符';
        }
        
        // 验证命令
        if (empty($data['command'])) {
            $errors[] = '执行命令不能为空';
        } elseif (!self::validateCommand($data['command'])) {
            $errors[] = '命令包含不安全的内容';
        }
        
        // 验证Cron表达式
        if (empty($data['cron_expression'])) {
            $errors[] = 'Cron表达式不能为空';
        } elseif (!self::validateCronExpression($data['cron_expression'])) {
            $errors[] = 'Cron表达式格式不正确';
        }
        
        // 验证超时时间
        if (isset($data['timeout_seconds'])) {
            $timeout = (int)$data['timeout_seconds'];
            if ($timeout < 1 || $timeout > 3600) {
                $errors[] = '超时时间必须在1-3600秒之间';
            }
        }
        
        // 验证重试次数
        if (isset($data['max_retries'])) {
            $retries = (int)$data['max_retries'];
            if ($retries < 0 || $retries > 10) {
                $errors[] = '重试次数必须在0-10次之间';
            }
        }
        
        // 验证状态
        if (isset($data['status'])) {
            $allowedStatuses = ['enabled', 'disabled'];
            if (!in_array($data['status'], $allowedStatuses)) {
                $errors[] = '无效的任务状态';
            }
        }
        
        return $errors;
    }
    
    /**
     * 记录安全事件
     */
    public static function logSecurityEvent($event, $details = []) {
        $logger = new Logger();
        
        $context = array_merge([
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ], $details);
        
        $logger->warning("安全事件: $event", $context);
    }
    
    /**
     * 检查请求频率限制
     */
    public static function checkRateLimit($identifier, $maxRequests = 60, $timeWindow = 60) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $key = "rate_limit_$identifier";
        $now = time();
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [];
        }
        
        // 清理过期的请求记录
        $_SESSION[$key] = array_filter($_SESSION[$key], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // 检查是否超过限制
        if (count($_SESSION[$key]) >= $maxRequests) {
            self::logSecurityEvent('Rate limit exceeded', [
                'identifier' => $identifier,
                'requests' => count($_SESSION[$key]),
                'limit' => $maxRequests
            ]);
            return false;
        }
        
        // 记录当前请求
        $_SESSION[$key][] = $now;
        
        return true;
    }
    
    /**
     * 验证文件上传安全性
     */
    public static function validateFileUpload($file) {
        $errors = [];
        
        // 检查文件大小
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file['size'] > $maxSize) {
            $errors[] = '文件大小不能超过10MB';
        }
        
        // 检查文件类型
        $allowedTypes = ['text/plain', 'application/json', 'text/csv'];
        if (!in_array($file['type'], $allowedTypes)) {
            $errors[] = '不支持的文件类型';
        }
        
        // 检查文件扩展名
        $allowedExtensions = ['txt', 'json', 'csv', 'log'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = '不支持的文件扩展名';
        }
        
        return $errors;
    }
}
