RewriteEngine On

# 重定向所有请求到index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全设置
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# 禁止访问敏感文件
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>
