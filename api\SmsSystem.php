<?php
$wsdl = 'http://dxpt.sc/services/IMsInterfaceNewService?wsdl';
$systemId = 'ed47f8d43acec3ee48d7409146bef5c1';
function SMS_sendmessage($toUserMobile, $content, $fromUserName) {
    global $wsdl,$systemId;
    try {
        $client = new SoapClient($wsdl);
    } catch (Exception $e) {
        error_log("SoapClient 初始化失败：\n" . $e->getMessage());
        return false;
    }
    
    $request = [
        'systemId' => $systemId,
        'extensionNo' => '039',
        'toUserMobile' => $toUserMobile,
        'content' => $content,
        'fromUserName' => $fromUserName
    ];
    
    $response = $client->sendMessage($request);
    $xmlString = $response->sendMessageResult;
    $xml = new SimpleXMLElement($xmlString);
    $ret = [
        'renturncode' => (string)$xml->sminfo_return->renturncode,
        'error_code' => (string)$xml->sminfo_return->error_code,
        'sm_id' => (string)$xml->sminfo_return->sm_id,
        'message' => (string)$xml->sminfo_return->message,
    ];
    return json_encode($ret);
}

function SMS_sreportStatus($smId) {
    global $wsdl, $systemId;
    try {
        $client = new SoapClient($wsdl);
    } catch (Exception $e) {
        error_log("SoapClient 初始化失败：\n" . $e->getMessage());
        return false;
    }
    
    $request = [
        'systemId' => $systemId,
        'extensionNo' => '039',
        'smId' => $smId
    ];
    
    $response = $client->report($request);
    
    // 将XML解析为数组
    $xml = simplexml_load_string($response->reportResult);
    $reports = [];
    foreach ($xml->sminfo_return->reports->report as $report) {
        $reports[] = [
            'sm_id' => (string)$report->sm_id,
            'tousermobile' => (string)$report->tousermobile,
            'state' => (string)$report->state
        ];
    }
    // 返回JSON格式
    $result = [
        'renturncode' => (string)$xml->sminfo_return->renturncode,
        'error_code' => (string)$xml->sminfo_return->error_code,
        'message' => (string)$xml->sminfo_return->error_code,
        'reports' => $reports
    ];
    
    return json_encode($result);
}

$toUserMobile = $_POST['toUserMobile'] ?? '';
$content = $_POST['content'] ?? '';
$fromUserName = $_POST['fromUserName'] ?? '';
$response = SMS_sendmessage($toUserMobile, $content, $fromUserName);

echo $toUserMobile;
echo $content;
echo $fromUserName;

//echo $response;


//$response = SMS_sendmessage('18682679030,15328553363', '测试内容', '岳池县公安局云枢');
//print_r($response);
//$response = SMS_sreportStatus('1941037332626935808');
//print_r($response);
?>