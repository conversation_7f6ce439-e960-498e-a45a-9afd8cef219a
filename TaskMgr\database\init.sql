-- TaskMgr 计划任务管理器数据库初始化脚本
-- 适用于MySQL 5.7+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS taskmgr CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE taskmgr;

-- 创建任务表
CREATE TABLE IF NOT EXISTS taskmgr_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '任务名称',
    description TEXT COMMENT '任务描述',
    command TEXT NOT NULL COMMENT '执行命令',
    cron_expression VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
    status ENUM('enabled', 'disabled', 'running', 'completed', 'failed') DEFAULT 'enabled' COMMENT '任务状态',
    last_run_time DATETIME NULL COMMENT '上次执行时间',
    next_run_time DATETIME NULL COMMENT '下次执行时间',
    run_count INT DEFAULT 0 COMMENT '执行次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    fail_count INT DEFAULT 0 COMMENT '失败次数',
    timeout_seconds INT DEFAULT 300 COMMENT '超时时间（秒）',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 3 COMMENT '最大重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
    
    INDEX idx_status (status),
    INDEX idx_next_run_time (next_run_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='计划任务表';

-- 创建任务执行日志表
CREATE TABLE IF NOT EXISTS taskmgr_task_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL COMMENT '任务ID',
    execution_id VARCHAR(36) NOT NULL COMMENT '执行ID（UUID）',
    status ENUM('running', 'success', 'failed', 'timeout', 'cancelled') NOT NULL COMMENT '执行状态',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NULL COMMENT '结束时间',
    duration INT NULL COMMENT '执行时长（秒）',
    output TEXT COMMENT '执行输出',
    error_message TEXT COMMENT '错误信息',
    exit_code INT NULL COMMENT '退出代码',
    memory_usage INT NULL COMMENT '内存使用（KB）',
    cpu_usage DECIMAL(5,2) NULL COMMENT 'CPU使用率',
    retry_attempt INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (task_id) REFERENCES taskmgr_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_execution_id (execution_id)
) ENGINE=InnoDB COMMENT='任务执行日志表';

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS taskmgr_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO taskmgr_config (config_key, config_value, description) VALUES
('max_concurrent_tasks', '5', '最大并发任务数'),
('default_timeout', '300', '默认超时时间（秒）'),
('log_retention_days', '30', '日志保留天数'),
('enable_email_notifications', '0', '是否启用邮件通知'),
('email_smtp_host', '', 'SMTP服务器地址'),
('email_smtp_port', '587', 'SMTP端口'),
('email_username', '', '邮箱用户名'),
('email_password', '', '邮箱密码'),
('notification_emails', '', '通知邮箱列表（逗号分隔）');

-- 创建任务锁表（防止重复执行）
CREATE TABLE IF NOT EXISTS taskmgr_task_locks (
    task_id INT NOT NULL PRIMARY KEY,
    locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    locked_by VARCHAR(100) NOT NULL COMMENT '锁定者（进程ID或标识）',
    expires_at TIMESTAMP NOT NULL COMMENT '锁过期时间',
    
    FOREIGN KEY (task_id) REFERENCES taskmgr_tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='任务锁表';

-- 创建示例任务
INSERT INTO taskmgr_tasks (name, description, command, cron_expression, status) VALUES
('系统状态检查', '每5分钟检查一次系统状态', 'df -h && free -m', '*/5 * * * *', 'enabled'),
('日志清理', '每天凌晨2点清理过期日志', 'find /var/log -name "*.log" -mtime +7 -delete', '0 2 * * *', 'enabled'),
('数据库备份', '每天凌晨3点备份数据库', 'mysqldump -u root -p taskmgr > /backup/taskmgr_$(date +%Y%m%d).sql', '0 3 * * *', 'disabled');
