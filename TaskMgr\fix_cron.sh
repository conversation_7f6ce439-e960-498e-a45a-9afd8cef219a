#!/bin/bash
#
# TaskMgr Cron修复脚本 - 专门针对宝塔面板
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "TaskMgr Cron修复脚本（宝塔面板专用）"
echo "=================================="
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INDEX_FILE="$SCRIPT_DIR/index.php"

log_info "TaskMgr路径: $SCRIPT_DIR"
log_info "主文件: $INDEX_FILE"

# 检查index.php文件
if [ ! -f "$INDEX_FILE" ]; then
    log_error "未找到index.php文件: $INDEX_FILE"
    exit 1
fi

# 根据PHP.ini路径确定正确的PHP路径
log_info "检测宝塔面板PHP路径..."

# 宝塔面板常见的PHP路径
BT_PHP_PATHS=(
    "/www/server/php/80/bin/php"
    "/www/server/php/81/bin/php"
    "/www/server/php/74/bin/php"
    "/www/server/php/73/bin/php"
    "/www/server/php/82/bin/php"
    "/www/server/php/56/bin/php"
    "/www/server/php/70/bin/php"
    "/www/server/php/71/bin/php"
    "/www/server/php/72/bin/php"
)

PHP_BIN=""
for php_path in "${BT_PHP_PATHS[@]}"; do
    if [ -x "$php_path" ]; then
        version=$($php_path -v 2>/dev/null | head -n1)
        if [ $? -eq 0 ] && [[ "$version" == *"PHP"* ]]; then
            log_success "找到宝塔PHP: $php_path"
            log_info "版本: $version"
            PHP_BIN="$php_path"
            break
        fi
    fi
done

if [ -z "$PHP_BIN" ]; then
    log_error "未找到宝塔面板的PHP可执行文件"
    echo ""
    echo "请检查以下路径是否存在："
    for path in "${BT_PHP_PATHS[@]}"; do
        echo "  $path"
    done
    echo ""
    echo "或者手动指定PHP路径："
    read -p "请输入PHP可执行文件的完整路径: " MANUAL_PHP_PATH
    if [ -n "$MANUAL_PHP_PATH" ] && [ -x "$MANUAL_PHP_PATH" ]; then
        PHP_BIN="$MANUAL_PHP_PATH"
        log_success "使用手动指定的PHP路径: $PHP_BIN"
    else
        log_error "指定的PHP路径无效"
        exit 1
    fi
fi

# 测试PHP脚本
log_info "测试TaskMgr脚本..."
echo "执行命令: $PHP_BIN $INDEX_FILE cron"
TEST_OUTPUT=$($PHP_BIN "$INDEX_FILE" cron 2>&1)
TEST_EXIT_CODE=$?

if [ $TEST_EXIT_CODE -eq 0 ]; then
    log_success "TaskMgr脚本测试成功"
    if [ -n "$TEST_OUTPUT" ]; then
        echo "输出: $TEST_OUTPUT"
    fi
else
    log_warning "TaskMgr脚本测试有问题，退出码: $TEST_EXIT_CODE"
    if [ -n "$TEST_OUTPUT" ]; then
        echo "错误输出: $TEST_OUTPUT"
    fi
    echo ""
    read -p "是否继续设置Cron？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "设置已取消"
        exit 1
    fi
fi

# 清理现有的错误Cron设置
log_info "清理现有的TaskMgr Cron设置..."
CURRENT_CRON=$(crontab -l 2>/dev/null)

if [ -n "$CURRENT_CRON" ]; then
    echo "当前crontab内容:"
    echo "$CURRENT_CRON"
    echo ""
    
    # 移除所有TaskMgr相关的行
    CLEANED_CRON=$(echo "$CURRENT_CRON" | grep -v "TaskMgr\|index.php\|cron.php")
    
    # 创建临时文件
    TEMP_CRON=$(mktemp)
    echo "$CLEANED_CRON" > "$TEMP_CRON"
    
    log_info "移除旧的TaskMgr Cron设置..."
else
    # 创建空的临时文件
    TEMP_CRON=$(mktemp)
    touch "$TEMP_CRON"
fi

# 添加新的正确的Cron设置
echo "" >> "$TEMP_CRON"
echo "# TaskMgr 计划任务管理器 - 使用正确的宝塔PHP路径" >> "$TEMP_CRON"
echo "* * * * * $PHP_BIN $INDEX_FILE cron >/dev/null 2>&1" >> "$TEMP_CRON"

# 安装新的crontab
log_info "安装新的Cron设置..."
if crontab "$TEMP_CRON"; then
    log_success "Cron任务设置成功！"
else
    log_error "设置Cron任务失败"
    rm -f "$TEMP_CRON"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_CRON"

echo ""
log_info "验证新的Cron设置..."
echo "当前crontab内容:"
echo "=================="
crontab -l | grep -A2 -B2 "TaskMgr\|index.php"

echo ""
echo "🎉 修复完成！"
echo ""
echo "重要信息："
echo "- 正确的PHP路径: $PHP_BIN"
echo "- TaskMgr路径: $INDEX_FILE"
echo "- 新的Cron命令: $PHP_BIN $INDEX_FILE cron"
echo ""
echo "接下来："
echo "1. 定时任务现在应该能正常执行了"
echo "2. 访问Web界面查看: http://10.152.110.168/TaskMgr/"
echo "3. 等待1-2分钟，然后检查任务是否自动执行"
echo ""
echo "验证方法："
echo "1. 访问: http://10.152.110.168/TaskMgr/test_cron.php"
echo "2. 创建一个测试任务"
echo "3. 等待1-2分钟查看是否自动执行"
echo "4. 检查执行日志确认正常工作"
echo ""
echo "如果还有问题："
echo "- 手动测试: $PHP_BIN $INDEX_FILE cron"
echo "- 查看系统日志: tail -f /var/log/cron"
echo "- 访问诊断页面: http://10.152.110.168/TaskMgr/diagnose.php"

echo ""
log_info "修复完成！请等待1-2分钟测试任务是否自动执行。"
