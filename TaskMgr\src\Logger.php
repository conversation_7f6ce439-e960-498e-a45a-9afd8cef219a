<?php
/**
 * 日志记录器
 */

class Logger {
    private $config;
    private $logPath;
    
    const LEVEL_DEBUG = 1;
    const LEVEL_INFO = 2;
    const LEVEL_WARNING = 3;
    const LEVEL_ERROR = 4;
    
    private $levels = [
        'debug' => self::LEVEL_DEBUG,
        'info' => self::LEVEL_INFO,
        'warning' => self::LEVEL_WARNING,
        'error' => self::LEVEL_ERROR
    ];
    
    public function __construct() {
        $this->config = require __DIR__ . '/../config/app.php';
        $this->logPath = $this->config['log']['path'];
        
        // 确保日志目录存在
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * 记录调试信息
     */
    public function debug($message, $context = []) {
        $this->log('debug', $message, $context);
    }
    
    /**
     * 记录一般信息
     */
    public function info($message, $context = []) {
        $this->log('info', $message, $context);
    }
    
    /**
     * 记录警告信息
     */
    public function warning($message, $context = []) {
        $this->log('warning', $message, $context);
    }
    
    /**
     * 记录错误信息
     */
    public function error($message, $context = []) {
        $this->log('error', $message, $context);
    }
    
    /**
     * 记录日志
     */
    public function log($level, $message, $context = []) {
        $configLevel = $this->config['log']['level'] ?? 'info';
        
        // 检查日志级别
        if ($this->levels[$level] < $this->levels[$configLevel]) {
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $pid = getmypid();
        $memory = round(memory_get_usage() / 1024 / 1024, 2); // MB
        
        // 格式化上下文信息
        $contextStr = '';
        if (!empty($context)) {
            $contextStr = ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        
        $logEntry = sprintf(
            "[%s] [%s] [PID:%d] [MEM:%sMB] %s%s\n",
            $timestamp,
            strtoupper($level),
            $pid,
            $memory,
            $message,
            $contextStr
        );
        
        // 写入日志文件
        $logFile = $this->logPath . 'taskmgr_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // 清理过期日志
        $this->cleanupOldLogs();
    }
    
    /**
     * 清理过期日志
     */
    private function cleanupOldLogs() {
        $maxFiles = $this->config['log']['max_files'] ?? 30;
        $files = glob($this->logPath . 'taskmgr_*.log');
        
        if (count($files) <= $maxFiles) {
            return;
        }
        
        // 按修改时间排序
        usort($files, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        
        // 删除最旧的文件
        $filesToDelete = array_slice($files, 0, count($files) - $maxFiles);
        foreach ($filesToDelete as $file) {
            unlink($file);
        }
    }
    
    /**
     * 获取日志文件列表
     */
    public function getLogFiles() {
        $files = glob($this->logPath . 'taskmgr_*.log');
        
        $result = [];
        foreach ($files as $file) {
            $result[] = [
                'filename' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'modified' => filemtime($file)
            ];
        }
        
        // 按修改时间倒序排列
        usort($result, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        return $result;
    }
    
    /**
     * 读取日志文件内容
     */
    public function readLogFile($filename, $lines = 100) {
        $filepath = $this->logPath . $filename;
        
        if (!file_exists($filepath)) {
            throw new Exception("日志文件不存在: $filename");
        }
        
        // 读取最后N行
        $file = new SplFileObject($filepath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);
        
        $content = [];
        while (!$file->eof()) {
            $line = trim($file->current());
            if (!empty($line)) {
                $content[] = $line;
            }
            $file->next();
        }
        
        return $content;
    }
    
    /**
     * 搜索日志内容
     */
    public function searchLogs($keyword, $level = null, $date = null, $limit = 100) {
        $pattern = $this->logPath . 'taskmgr_';
        
        if ($date) {
            $pattern .= $date . '.log';
            $files = glob($pattern);
        } else {
            $pattern .= '*.log';
            $files = glob($pattern);
            // 按日期倒序
            rsort($files);
        }
        
        $results = [];
        $count = 0;
        
        foreach ($files as $file) {
            if ($count >= $limit) {
                break;
            }
            
            $handle = fopen($file, 'r');
            if (!$handle) {
                continue;
            }
            
            while (($line = fgets($handle)) !== false && $count < $limit) {
                // 检查关键词
                if (stripos($line, $keyword) === false) {
                    continue;
                }
                
                // 检查日志级别
                if ($level && stripos($line, '[' . strtoupper($level) . ']') === false) {
                    continue;
                }
                
                $results[] = [
                    'file' => basename($file),
                    'line' => trim($line),
                    'timestamp' => $this->extractTimestamp($line)
                ];
                
                $count++;
            }
            
            fclose($handle);
        }
        
        return $results;
    }
    
    /**
     * 从日志行中提取时间戳
     */
    private function extractTimestamp($line) {
        if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    /**
     * 获取日志统计信息
     */
    public function getLogStats($date = null) {
        $pattern = $this->logPath . 'taskmgr_';
        
        if ($date) {
            $pattern .= $date . '.log';
        } else {
            $pattern .= date('Y-m-d') . '.log';
        }
        
        $files = glob($pattern);
        
        $stats = [
            'total_lines' => 0,
            'debug' => 0,
            'info' => 0,
            'warning' => 0,
            'error' => 0
        ];
        
        foreach ($files as $file) {
            $handle = fopen($file, 'r');
            if (!$handle) {
                continue;
            }
            
            while (($line = fgets($handle)) !== false) {
                $stats['total_lines']++;
                
                if (stripos($line, '[DEBUG]') !== false) {
                    $stats['debug']++;
                } elseif (stripos($line, '[INFO]') !== false) {
                    $stats['info']++;
                } elseif (stripos($line, '[WARNING]') !== false) {
                    $stats['warning']++;
                } elseif (stripos($line, '[ERROR]') !== false) {
                    $stats['error']++;
                }
            }
            
            fclose($handle);
        }
        
        return $stats;
    }
}
