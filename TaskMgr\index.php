<?php
/**
 * 简单计划任务管理器
 * 单文件版本，包含所有功能
 */

// 如果是命令行调用且参数为cron，则执行计划任务
if (php_sapi_name() === 'cli' && isset($argv[1]) && $argv[1] === 'cron') {
    runCronTasks();
    exit;
}

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'taskmgr',
    'username' => 'root',
    'password' => 'YC@yc110',
    'charset' => 'utf8mb4'
];

// 数据库连接
function getDB() {
    global $db_config;
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['database']};charset={$db_config['charset']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    return $pdo;
}

// 初始化数据库表
function initDatabase() {
    $pdo = getDB();
    
    // 创建任务表
    $sql = "CREATE TABLE IF NOT EXISTS tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        command TEXT NOT NULL,
        cron_expression VARCHAR(100) NOT NULL,
        status ENUM('enabled', 'disabled') DEFAULT 'enabled',
        last_run DATETIME NULL,
        next_run DATETIME NULL,
        run_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // 创建日志表
    $sql = "CREATE TABLE IF NOT EXISTS task_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        status ENUM('success', 'failed') NOT NULL,
        output TEXT,
        error_message TEXT,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        duration DECIMAL(10,2) NOT NULL
    )";
    $pdo->exec($sql);
}

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $pdo = getDB();
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_task':
                $name = $_POST['name'];
                $command = $_POST['command'];
                $cronExpression = $_POST['cron_expression'];

                // 检查是否为单次执行任务
                if (strpos($cronExpression, '@once') === 0) {
                    if ($cronExpression === '@once') {
                        // 立即执行的单次任务
                        $stmt = $pdo->prepare("INSERT INTO tasks (name, command, cron_expression, status) VALUES (?, ?, ?, 'disabled')");
                        $stmt->execute([$name, $command, '* * * * *']); // 使用通用cron表达式，但状态为禁用

                        $taskId = $pdo->lastInsertId();

                    // 立即执行任务
                    $start_time = date('Y-m-d H:i:s');
                    $start = microtime(true);

                    // 执行命令
                    $output = '';
                    $exit_code = 0;

                    if (function_exists('exec')) {
                        exec($command . ' 2>&1', $output_lines, $exit_code);
                        $output = implode("\n", $output_lines);
                    } elseif (function_exists('shell_exec')) {
                        $output = shell_exec($command . ' 2>&1');
                        $exit_code = 0;
                    } else {
                        $output = "错误：命令执行函数被禁用";
                        $exit_code = -1;
                    }

                    if (empty($output)) {
                        $output = "命令执行完成，无输出内容";
                    }

                    $end = microtime(true);
                    $duration = round($end - $start, 2);
                    $end_time = date('Y-m-d H:i:s');
                    $status = ($exit_code === 0) ? 'success' : 'failed';

                    // 记录执行日志
                    $stmt = $pdo->prepare("INSERT INTO task_logs (task_id, status, output, error_message, start_time, end_time, duration) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $error_message = ($exit_code !== 0) ? "命令执行失败，退出码: $exit_code" : null;
                    $stmt->execute([$taskId, $status, $output, $error_message, $start_time, $end_time, $duration]);

                    // 更新任务统计
                    $stmt = $pdo->prepare("UPDATE tasks SET last_run = ?, run_count = 1 WHERE id = ?");
                    $stmt->execute([$start_time, $taskId]);

                    echo json_encode([
                        'success' => true,
                        'message' => '单次任务创建并执行完成',
                        'output' => $output,
                        'status' => $status,
                        'duration' => $duration
                    ]);
                } else {
                    // 普通定时任务
                    $stmt = $pdo->prepare("INSERT INTO tasks (name, command, cron_expression) VALUES (?, ?, ?)");
                    $stmt->execute([$name, $command, $cronExpression]);
                    echo json_encode(['success' => true, 'message' => '定时任务添加成功']);
                }
                break;

            case 'edit_task':
                $taskId = $_POST['task_id'];
                $name = $_POST['name'];
                $command = $_POST['command'];
                $cronExpression = $_POST['cron_expression'];

                $stmt = $pdo->prepare("UPDATE tasks SET name = ?, command = ?, cron_expression = ? WHERE id = ?");
                $stmt->execute([$name, $command, $cronExpression, $taskId]);
                echo json_encode(['success' => true, 'message' => '任务修改成功']);
                break;

            case 'get_task':
                $taskId = $_POST['id'];
                $stmt = $pdo->prepare("SELECT * FROM tasks WHERE id = ?");
                $stmt->execute([$taskId]);
                $task = $stmt->fetch();

                if ($task) {
                    echo json_encode(['success' => true, 'task' => $task]);
                } else {
                    echo json_encode(['success' => false, 'message' => '任务不存在']);
                }
                break;

            case 'delete_task':
                $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => '任务删除成功']);
                break;
                
            case 'toggle_task':
                $stmt = $pdo->prepare("UPDATE tasks SET status = IF(status = 'enabled', 'disabled', 'enabled') WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => '任务状态更新成功']);
                break;
                
            case 'run_task':
                $stmt = $pdo->prepare("SELECT * FROM tasks WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                $task = $stmt->fetch();

                if ($task) {
                    $start_time = date('Y-m-d H:i:s');
                    $start = microtime(true);

                    // 执行命令并捕获退出码
                    $command = $task['command'] . ' 2>&1';
                    $output = '';
                    $exit_code = 0;

                    // 尝试不同的执行函数
                    if (function_exists('exec')) {
                        exec($command, $output_lines, $exit_code);
                        $output = implode("\n", $output_lines);
                    } elseif (function_exists('shell_exec')) {
                        $output = shell_exec($command);
                        $exit_code = 0; // shell_exec无法获取退出码，假设成功
                    } elseif (function_exists('system')) {
                        ob_start();
                        $exit_code = system($command);
                        $output = ob_get_clean();
                    } elseif (function_exists('passthru')) {
                        ob_start();
                        passthru($command, $exit_code);
                        $output = ob_get_clean();
                    } else {
                        $output = "错误：所有命令执行函数都被禁用";
                        $exit_code = -1;
                    }

                    // 如果没有输出，添加默认信息
                    if (empty($output)) {
                        $output = "命令执行完成，无输出内容";
                    }

                    $end = microtime(true);
                    $duration = round($end - $start, 2);
                    $end_time = date('Y-m-d H:i:s');

                    // 根据退出码判断状态
                    $status = ($exit_code === 0) ? 'success' : 'failed';

                    // 记录日志
                    try {
                        $stmt = $pdo->prepare("INSERT INTO task_logs (task_id, status, output, error_message, start_time, end_time, duration) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $error_message = ($exit_code !== 0) ? "命令执行失败，退出码: $exit_code" : null;
                        $stmt->execute([$task['id'], $status, $output, $error_message, $start_time, $end_time, $duration]);

                        // 更新任务统计
                        $stmt = $pdo->prepare("UPDATE tasks SET last_run = ?, run_count = run_count + 1 WHERE id = ?");
                        $stmt->execute([$start_time, $task['id']]);

                        echo json_encode([
                            'success' => true,
                            'message' => '任务执行完成',
                            'output' => $output,
                            'status' => $status,
                            'duration' => $duration,
                            'exit_code' => $exit_code
                        ]);
                    } catch (PDOException $e) {
                        echo json_encode(['success' => false, 'message' => '日志记录失败: ' . $e->getMessage()]);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => '任务不存在']);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => '未知操作']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 获取任务列表
function getTasks() {
    $pdo = getDB();
    try {
        $stmt = $pdo->query("SELECT * FROM tasks ORDER BY created_at DESC");
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        // 如果表不存在，返回空数组
        return [];
    }
}

// 获取日志
function getLogs($limit = 50) {
    $pdo = getDB();
    $limit = (int)$limit; // 确保是整数
    try {
        $sql = "
            SELECT l.*, t.name as task_name
            FROM task_logs l
            LEFT JOIN tasks t ON l.task_id = t.id
            ORDER BY l.start_time DESC
            LIMIT $limit
        ";
        $stmt = $pdo->query($sql);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        // 如果表不存在，返回空数组
        return [];
    }
}

// 检查Cron表达式是否匹配当前时间
function cronMatches($cronExpression, $timestamp = null) {
    if ($timestamp === null) {
        $timestamp = time();
    }

    $parts = explode(' ', $cronExpression);
    if (count($parts) !== 5) {
        return false;
    }

    list($minute, $hour, $day, $month, $weekday) = $parts;

    $currentMinute = (int)date('i', $timestamp);
    $currentHour = (int)date('G', $timestamp);
    $currentDay = (int)date('j', $timestamp);
    $currentMonth = (int)date('n', $timestamp);
    $currentWeekday = (int)date('w', $timestamp);

    return cronFieldMatches($minute, $currentMinute, 0, 59) &&
           cronFieldMatches($hour, $currentHour, 0, 23) &&
           cronFieldMatches($day, $currentDay, 1, 31) &&
           cronFieldMatches($month, $currentMonth, 1, 12) &&
           cronFieldMatches($weekday, $currentWeekday, 0, 7);
}

// 检查Cron字段是否匹配
function cronFieldMatches($field, $value, $min, $max) {
    if ($field === '*') {
        return true;
    }

    if (strpos($field, '/') !== false) {
        list($range, $step) = explode('/', $field);
        if ($range === '*') {
            return $value % $step === 0;
        }
    }

    if (strpos($field, ',') !== false) {
        $values = explode(',', $field);
        return in_array($value, array_map('intval', $values));
    }

    if (strpos($field, '-') !== false) {
        list($start, $end) = explode('-', $field);
        return $value >= $start && $value <= $end;
    }

    return (int)$field === $value;
}

// 执行计划任务
function runCronTasks() {
    $pdo = getDB();

    // 获取所有启用的任务
    $stmt = $pdo->query("SELECT * FROM tasks WHERE status = 'enabled'");
    $tasks = $stmt->fetchAll();

    $executedCount = 0;

    foreach ($tasks as $task) {
        if (cronMatches($task['cron_expression'])) {
            echo "执行任务: {$task['name']}\n";

            $start_time = date('Y-m-d H:i:s');
            $start = microtime(true);

            // 执行命令并捕获退出码
            $command = $task['command'] . ' 2>&1';
            $output = '';
            $exit_code = 0;

            // 尝试不同的执行函数
            if (function_exists('exec')) {
                exec($command, $output_lines, $exit_code);
                $output = implode("\n", $output_lines);
            } elseif (function_exists('shell_exec')) {
                $output = shell_exec($command);
                $exit_code = 0; // shell_exec无法获取退出码，假设成功
            } elseif (function_exists('system')) {
                ob_start();
                $exit_code = system($command);
                $output = ob_get_clean();
            } elseif (function_exists('passthru')) {
                ob_start();
                passthru($command, $exit_code);
                $output = ob_get_clean();
            } else {
                $output = "错误：所有命令执行函数都被禁用";
                $exit_code = -1;
            }

            // 如果没有输出，添加默认信息
            if (empty($output)) {
                $output = "命令执行完成，无输出内容";
            }

            $end = microtime(true);
            $duration = round($end - $start, 2);
            $end_time = date('Y-m-d H:i:s');

            // 根据退出码判断状态
            $status = ($exit_code === 0) ? 'success' : 'failed';

            // 记录日志
            $stmt = $pdo->prepare("INSERT INTO task_logs (task_id, status, output, error_message, start_time, end_time, duration) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $error_message = ($exit_code !== 0) ? "命令执行失败，退出码: $exit_code" : null;
            $stmt->execute([$task['id'], $status, $output, $error_message, $start_time, $end_time, $duration]);

            // 更新任务统计
            $stmt = $pdo->prepare("UPDATE tasks SET last_run = ?, run_count = run_count + 1 WHERE id = ?");
            $stmt->execute([$start_time, $task['id']]);

            $executedCount++;
            echo "任务执行完成，耗时: {$duration}秒\n";
        }
    }

    if ($executedCount === 0) {
        echo "没有需要执行的任务\n";
    } else {
        echo "共执行了 $executedCount 个任务\n";
    }
}

// 初始化数据库
initDatabase();

// 获取数据
$tasks = getTasks();
$logs = getLogs();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单计划任务管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #4a5568;
        }

        .header p {
            font-size: 1.1em;
            color: #718096;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .quick-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .quick-btn.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .quick-btn.warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .quick-btn.danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .form-section {
            background: #f7fafc;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #4a5568;
            font-size: 14px;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .preset-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .preset-btn {
            background: #e2e8f0;
            color: #4a5568;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #cbd5e0;
        }

        .task-list {
            background: #f7fafc;
            border-radius: 12px;
            overflow: hidden;
        }

        .task-item {
            background: white;
            margin: 10px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .task-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .task-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .task-name {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            flex: 1;
        }

        .task-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-enabled {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-disabled {
            background: #fed7d7;
            color: #742a2a;
        }

        .task-details {
            color: #718096;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .task-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
            color: white;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background: #e53e3e;
        }

        .alert {
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            display: none;
            font-weight: bold;
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #718096;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #4a5568;
        }

        .section-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 计划任务管理器</h1>
            <p>简单易用的定时任务管理工具</p>
            <?php
            // 检查命令执行函数是否可用
            $functions = ['exec', 'shell_exec', 'system', 'passthru'];
            $available = false;
            foreach ($functions as $func) {
                if (function_exists($func)) {
                    $available = true;
                    break;
                }
            }

            if (!$available) {
                echo '<div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 6px; margin-top: 15px;">';
                echo '⚠️ 命令执行函数被禁用，任务无法正常执行。';
                echo '<a href="setup_guide.php" style="color: #856404; text-decoration: underline; margin-left: 10px;">查看启用指南</a>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="alert alert-success" id="success-alert"></div>
        <div class="alert alert-error" id="error-alert"></div>

        <!-- 快速操作按钮 -->
        <div class="quick-actions">
            <button class="quick-btn" onclick="showAddForm()">
                ➕ 添加新任务
            </button>
            <button class="quick-btn success" onclick="runAllTasks()">
                ▶️ 执行所有任务
            </button>
            <button class="quick-btn warning" onclick="showLogs()">
                📊 查看执行日志
            </button>
            <button class="quick-btn danger" onclick="showHelp()">
                ❓ 使用帮助
            </button>
        </div>

        <!-- 添加任务表单 -->
        <div class="card" id="add-form" style="display: none;">
            <div class="section-title">添加新任务</div>
            <div class="form-section">
                <form id="task-form">
                    <div class="form-group">
                        <label>📝 任务名称</label>
                        <input type="text" name="name" required placeholder="给你的任务起个名字，比如：每日备份">
                    </div>

                    <div class="form-group">
                        <label>⚡ 选择任务类型</label>
                        <select id="task-type" onchange="updateCommand()">
                            <option value="">请选择任务类型</option>
                            <optgroup label="📁 文件操作">
                                <option value="backup">文件备份</option>
                                <option value="cleanup">清理临时文件</option>
                                <option value="sync">文件同步</option>
                            </optgroup>
                            <optgroup label="💻 脚本执行">
                                <option value="php">执行PHP脚本</option>
                                <option value="python">执行Python脚本</option>
                                <option value="shell">执行Shell脚本</option>
                                <option value="node">执行Node.js脚本</option>
                            </optgroup>
                            <optgroup label="🗄️ 数据库操作">
                                <option value="mysql_backup">MySQL数据库备份</option>
                                <option value="mysql_optimize">MySQL数据库优化</option>
                            </optgroup>
                            <optgroup label="🔍 系统监控">
                                <option value="check">系统状态检查</option>
                                <option value="disk_check">磁盘空间检查</option>
                                <option value="log_check">日志检查</option>
                                <option value="service_check">服务状态检查</option>
                            </optgroup>
                            <optgroup label="🌐 网络操作">
                                <option value="curl">HTTP请求</option>
                                <option value="ping">网络连通性测试</option>
                                <option value="wget">文件下载</option>
                            </optgroup>
                            <optgroup label="⚙️ 其他">
                                <option value="custom">自定义命令</option>
                            </optgroup>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>💻 执行命令</label>
                        <textarea name="command" id="command" required placeholder="系统会根据你选择的任务类型自动填写，你也可以手动修改"></textarea>
                    </div>

                    <div class="form-group">
                        <label>⏰ 执行时间设置</label>

                        <div style="margin-bottom: 15px;">
                            <label style="font-size: 14px; color: #666;">执行频率</label>
                            <select id="frequency" onchange="updateTimeOptions()" style="margin-bottom: 10px;">
                                <option value="">请选择执行频率</option>
                                <option value="once">🔥 单次执行（立即执行一次）</option>
                                <option value="minute">⏱️ 每分钟</option>
                                <option value="hour">🕐 每小时</option>
                                <option value="day">📅 每天</option>
                                <option value="week">📆 每周</option>
                                <option value="month">🗓️ 每月</option>
                            </select>
                        </div>

                        <!-- 单次执行设置 -->
                        <div id="once-options" style="display: none; margin-bottom: 15px;">
                            <div style="background: #e8f4fd; padding: 15px; border-radius: 8px;">
                                <div style="color: #0c5460; font-weight: bold; margin-bottom: 10px;">🔥 单次执行模式</div>
                                <div style="color: #0c5460; font-size: 14px; margin-bottom: 15px;">
                                    任务只执行一次，执行完成后自动禁用。<br>
                                    适用于：一次性脚本、临时任务、测试命令等。
                                </div>

                                <div style="margin-bottom: 10px;">
                                    <label style="font-size: 14px; color: #666;">执行时间</label>
                                    <select id="once-timing" onchange="updateOnceOptions()">
                                        <option value="immediate">立即执行</option>
                                        <option value="scheduled">指定时间执行</option>
                                    </select>
                                </div>

                                <div id="once-scheduled" style="display: none;">
                                    <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                        <div>
                                            <label style="font-size: 14px; color: #666;">日期</label>
                                            <input type="date" id="once-date" style="width: 150px;" min="<?= date('Y-m-d') ?>">
                                        </div>
                                        <div>
                                            <label style="font-size: 14px; color: #666;">时间</label>
                                            <input type="time" id="once-time" style="width: 120px;" value="<?= date('H:i') ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分钟设置 -->
                        <div id="minute-options" style="display: none; margin-bottom: 15px;">
                            <label style="font-size: 14px; color: #666;">每隔几分钟执行一次</label>
                            <select id="minute-interval">
                                <option value="1">每1分钟</option>
                                <option value="5">每5分钟</option>
                                <option value="10">每10分钟</option>
                                <option value="15">每15分钟</option>
                                <option value="30">每30分钟</option>
                            </select>
                        </div>

                        <!-- 小时设置 -->
                        <div id="hour-options" style="display: none; margin-bottom: 15px;">
                            <label style="font-size: 14px; color: #666;">每小时的第几分钟执行</label>
                            <select id="hour-minute">
                                <option value="0">整点执行（0分）</option>
                                <option value="15">15分</option>
                                <option value="30">30分</option>
                                <option value="45">45分</option>
                            </select>
                        </div>

                        <!-- 每天设置 -->
                        <div id="day-options" style="display: none; margin-bottom: 15px;">
                            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                <div>
                                    <label style="font-size: 14px; color: #666;">小时</label>
                                    <select id="day-hour">
                                        <?php for($h = 0; $h < 24; $h++): ?>
                                        <option value="<?= $h ?>" <?= $h == 2 ? 'selected' : '' ?>><?= sprintf('%02d', $h) ?>点</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div>
                                    <label style="font-size: 14px; color: #666;">分钟</label>
                                    <select id="day-minute">
                                        <?php for($m = 0; $m < 60; $m += 5): ?>
                                        <option value="<?= $m ?>" <?= $m == 0 ? 'selected' : '' ?>><?= sprintf('%02d', $m) ?>分</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 每周设置 -->
                        <div id="week-options" style="display: none; margin-bottom: 15px;">
                            <div style="margin-bottom: 10px;">
                                <label style="font-size: 14px; color: #666;">星期几</label>
                                <select id="week-day">
                                    <option value="0">星期日</option>
                                    <option value="1" selected>星期一</option>
                                    <option value="2">星期二</option>
                                    <option value="3">星期三</option>
                                    <option value="4">星期四</option>
                                    <option value="5">星期五</option>
                                    <option value="6">星期六</option>
                                </select>
                            </div>
                            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                <div>
                                    <label style="font-size: 14px; color: #666;">小时</label>
                                    <select id="week-hour">
                                        <?php for($h = 0; $h < 24; $h++): ?>
                                        <option value="<?= $h ?>" <?= $h == 2 ? 'selected' : '' ?>><?= sprintf('%02d', $h) ?>点</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div>
                                    <label style="font-size: 14px; color: #666;">分钟</label>
                                    <select id="week-minute">
                                        <?php for($m = 0; $m < 60; $m += 5): ?>
                                        <option value="<?= $m ?>" <?= $m == 0 ? 'selected' : '' ?>><?= sprintf('%02d', $m) ?>分</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 每月设置 -->
                        <div id="month-options" style="display: none; margin-bottom: 15px;">
                            <div style="margin-bottom: 10px;">
                                <label style="font-size: 14px; color: #666;">每月第几天</label>
                                <select id="month-day">
                                    <option value="1" selected>1号</option>
                                    <?php for($d = 2; $d <= 31; $d++): ?>
                                    <option value="<?= $d ?>"><?= $d ?>号</option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                                <div>
                                    <label style="font-size: 14px; color: #666;">小时</label>
                                    <select id="month-hour">
                                        <?php for($h = 0; $h < 24; $h++): ?>
                                        <option value="<?= $h ?>" <?= $h == 2 ? 'selected' : '' ?>><?= sprintf('%02d', $h) ?>点</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div>
                                    <label style="font-size: 14px; color: #666;">分钟</label>
                                    <select id="month-minute">
                                        <?php for($m = 0; $m < 60; $m += 5): ?>
                                        <option value="<?= $m ?>" <?= $m == 0 ? 'selected' : '' ?>><?= sprintf('%02d', $m) ?>分</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 预览 -->
                        <div id="time-preview" style="background: #e8f4fd; padding: 10px; border-radius: 6px; margin-top: 10px; display: none;">
                            <strong>⏰ 执行时间预览：</strong>
                            <span id="preview-text"></span>
                        </div>

                        <input type="hidden" name="cron_expression" id="cron_expression" required>
                    </div>

                    <div style="text-align: center; margin-top: 25px;">
                        <button type="submit" class="quick-btn">✅ 创建任务</button>
                        <button type="button" class="quick-btn warning" onclick="hideAddForm()" style="margin-left: 10px;">❌ 取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 编辑任务表单 -->
        <div class="card" id="edit-form" style="display: none;">
            <div class="section-title">编辑任务</div>
            <div class="form-section">
                <form id="edit-task-form">
                    <input type="hidden" id="edit-task-id" name="task_id">

                    <div class="form-group">
                        <label>📝 任务名称</label>
                        <input type="text" name="name" id="edit-name" required>
                    </div>

                    <div class="form-group">
                        <label>💻 执行命令</label>
                        <textarea name="command" id="edit-command" required></textarea>
                    </div>

                    <div class="form-group">
                        <label>⏰ Cron表达式</label>
                        <input type="text" name="cron_expression" id="edit-cron" required placeholder="例如：0 2 * * *">
                        <small>如需修改执行时间，建议删除任务后重新创建</small>
                    </div>

                    <div style="text-align: center; margin-top: 25px;">
                        <button type="submit" class="quick-btn">✅ 保存修改</button>
                        <button type="button" class="quick-btn warning" onclick="hideEditForm()" style="margin-left: 10px;">❌ 取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card" id="task-list">
            <div class="section-title">我的任务列表</div>
            <div class="task-list">
                <?php if (empty($tasks)): ?>
                <div class="empty-state">
                    <h3>🎯 还没有任务</h3>
                    <p>点击上面的"添加新任务"按钮来创建你的第一个定时任务吧！</p>
                </div>
                <?php else: ?>
                    <?php foreach ($tasks as $task): ?>
                    <div class="task-item">
                        <div class="task-header">
                            <div class="task-name">📋 <?= htmlspecialchars($task['name']) ?></div>
                            <div class="task-status status-<?= $task['status'] ?>">
                                <?= $task['status'] === 'enabled' ? '✅ 启用' : '⏸️ 禁用' ?>
                            </div>
                        </div>
                        <div class="task-details">
                            <div>💻 命令:
                                <?php
                                $command = $task['command'];
                                $shortCommand = htmlspecialchars(substr($command, 0, 80));
                                $fullCommand = htmlspecialchars($command);
                                $isLongCommand = strlen($command) > 80;
                                ?>
                                <span id="cmd-short-<?= $task['id'] ?>"><?= $shortCommand ?><?= $isLongCommand ? '...' : '' ?></span>
                                <?php if ($isLongCommand): ?>
                                <span id="cmd-full-<?= $task['id'] ?>" style="display: none;"><?= $fullCommand ?></span>
                                <button class="btn btn-primary" style="margin-left: 10px; padding: 2px 6px; font-size: 11px;"
                                        onclick="toggleCommand(<?= $task['id'] ?>)" id="cmd-btn-<?= $task['id'] ?>">
                                    📖 查看完整命令
                                </button>
                                <?php endif; ?>
                            </div>
                            <div>⏰ 执行时间: <?= htmlspecialchars($task['cron_expression']) ?></div>
                            <div>📊 执行次数: <?= $task['run_count'] ?> 次 | 上次执行: <?= $task['last_run'] ?: '从未执行' ?></div>
                        </div>
                        <div class="task-actions">
                            <button class="btn btn-success" onclick="runTask(<?= $task['id'] ?>)">
                                ▶️ 立即执行
                            </button>
                            <button class="btn btn-primary" onclick="editTask(<?= $task['id'] ?>)">
                                ✏️ 编辑
                            </button>
                            <button class="btn btn-warning" onclick="toggleTask(<?= $task['id'] ?>)">
                                <?= $task['status'] === 'enabled' ? '⏸️ 禁用' : '▶️ 启用' ?>
                            </button>
                            <button class="btn btn-danger" onclick="deleteTask(<?= $task['id'] ?>)">
                                🗑️ 删除
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- 执行日志 -->
        <div class="card" id="logs-section" style="display: none;">
            <div class="section-title">执行日志</div>
            <div class="task-list">
                <?php if (empty($logs)): ?>
                <div class="empty-state">
                    <h3>📝 暂无执行记录</h3>
                    <p>任务执行后会在这里显示详细的执行日志</p>
                </div>
                <?php else: ?>
                    <?php foreach ($logs as $log): ?>
                    <div class="task-item">
                        <div class="task-header">
                            <div class="task-name">📋 <?= htmlspecialchars($log['task_name'] ?: '未知任务') ?></div>
                            <div class="task-status <?= $log['status'] === 'success' ? 'status-enabled' : 'status-disabled' ?>">
                                <?= $log['status'] === 'success' ? '✅ 成功' : '❌ 失败' ?>
                            </div>
                        </div>
                        <div class="task-details">
                            <div>⏰ 执行时间: <?= $log['start_time'] ?></div>
                            <div>⏱️ 耗时: <?= $log['duration'] ?> 秒</div>
                            <div>📄 输出:
                                <?php
                                $output = $log['output'] ?: $log['error_message'] ?: '无输出';
                                $shortOutput = htmlspecialchars(substr($output, 0, 150));
                                $fullOutput = htmlspecialchars($output);
                                $isLong = strlen($output) > 150;
                                ?>
                                <span id="short-<?= $log['id'] ?>"><?= $shortOutput ?><?= $isLong ? '...' : '' ?></span>
                                <?php if ($isLong): ?>
                                <span id="full-<?= $log['id'] ?>" style="display: none;"><?= $fullOutput ?></span>
                                <br>
                                <button class="btn btn-primary" style="margin-top: 5px; padding: 4px 8px; font-size: 12px;"
                                        onclick="toggleOutput(<?= $log['id'] ?>)" id="btn-<?= $log['id'] ?>">
                                    📖 查看完整输出
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="hideLogs()">🔙 返回任务列表</button>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="card" id="help-section" style="display: none;">
            <div class="section-title">使用帮助</div>
            <div class="form-section">
                <h3>🚀 快速开始</h3>
                <ol style="line-height: 1.8; margin-bottom: 20px;">
                    <li>点击"添加新任务"按钮</li>
                    <li>选择任务类型（系统会自动填写命令）</li>
                    <li>选择执行时间（比如每天、每小时等）</li>
                    <li>点击"创建任务"完成</li>
                </ol>

                <h3>⚙️ 自动执行设置</h3>
                <p style="margin-bottom: 10px;">要让任务自动执行，需要设置系统定时器：</p>
                <div style="background: #f0f0f0; padding: 15px; border-radius: 8px; font-family: monospace; margin-bottom: 20px;">
                    crontab -e<br>
                    # 添加这一行：<br>
                    * * * * * /usr/bin/php <?= __FILE__ ?> cron >/dev/null 2>&1
                </div>

                <h3>📋 常用任务示例</h3>
                <ul style="line-height: 1.8;">
                    <li><strong>文件备份：</strong> 定期备份重要文件到安全位置</li>
                    <li><strong>清理临时文件：</strong> 自动清理系统临时文件释放空间</li>
                    <li><strong>系统检查：</strong> 定期检查系统状态和资源使用情况</li>
                    <li><strong>数据库备份：</strong> 定期备份数据库数据</li>
                </ul>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="hideHelp()">🔙 返回任务列表</button>
            </div>
        </div>
    </div>

    <script>
        // 显示/隐藏添加表单
        function showAddForm() {
            document.getElementById('add-form').style.display = 'block';
            document.getElementById('task-list').style.display = 'block';
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('add-form').scrollIntoView({ behavior: 'smooth' });
        }

        function hideAddForm() {
            document.getElementById('add-form').style.display = 'none';
        }

        // 显示/隐藏日志
        function showLogs() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('task-list').style.display = 'none';
            document.getElementById('logs-section').style.display = 'block';
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('logs-section').scrollIntoView({ behavior: 'smooth' });
        }

        function hideLogs() {
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('task-list').style.display = 'block';
        }

        // 显示/隐藏帮助
        function showHelp() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('task-list').style.display = 'none';
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('help-section').style.display = 'block';
            document.getElementById('help-section').scrollIntoView({ behavior: 'smooth' });
        }

        function hideHelp() {
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('task-list').style.display = 'block';
        }

        // 显示/隐藏编辑表单
        function showEditForm() {
            document.getElementById('add-form').style.display = 'none';
            document.getElementById('task-list').style.display = 'block';
            document.getElementById('logs-section').style.display = 'none';
            document.getElementById('help-section').style.display = 'none';
            document.getElementById('edit-form').style.display = 'block';
            document.getElementById('edit-form').scrollIntoView({ behavior: 'smooth' });
        }

        function hideEditForm() {
            document.getElementById('edit-form').style.display = 'none';
        }

        // 更新单次执行选项
        function updateOnceOptions() {
            const timing = document.getElementById('once-timing').value;
            const scheduledDiv = document.getElementById('once-scheduled');

            if (timing === 'scheduled') {
                scheduledDiv.style.display = 'block';
                // 设置默认日期为今天
                document.getElementById('once-date').value = new Date().toISOString().split('T')[0];
            } else {
                scheduledDiv.style.display = 'none';
            }

            updateCronExpression();
        }

        // 更新命令根据任务类型
        function updateCommand() {
            const taskType = document.getElementById('task-type').value;
            const commandField = document.getElementById('command');

            const commands = {
                // 文件操作
                'backup': 'tar -czf /backup/backup_$(date +%Y%m%d_%H%M%S).tar.gz /home',
                'cleanup': 'find /tmp -name "*.tmp" -mtime +1 -delete && find /var/log -name "*.log" -size +100M -delete',
                'sync': 'rsync -av /source/ /destination/',

                // 脚本执行
                'php': '/usr/bin/php /path/to/your/script.php',
                'python': '/usr/bin/python3 /path/to/your/script.py',
                'shell': '/bin/bash /path/to/your/script.sh',
                'node': '/usr/bin/node /path/to/your/script.js',

                // 数据库操作
                'mysql_backup': 'mysqldump -u root -p\'password\' database_name > /backup/db_$(date +%Y%m%d_%H%M%S).sql',
                'mysql_optimize': 'mysqlcheck -u root -p\'password\' --optimize --all-databases',

                // 系统监控
                'check': 'df -h && free -m && uptime',
                'disk_check': 'df -h | awk \'$5 > 80 {print $0}\'',
                'log_check': 'tail -n 100 /var/log/syslog | grep -i error',
                'service_check': 'systemctl status nginx mysql php-fpm',

                // 网络操作
                'curl': 'curl -s -o /dev/null -w "%{http_code}" https://example.com',
                'ping': 'ping -c 4 google.com',
                'wget': 'wget -O /tmp/file.zip https://example.com/file.zip',

                // 自定义
                'custom': ''
            };

            if (commands[taskType] !== undefined) {
                commandField.value = commands[taskType];
                if (taskType === 'custom') {
                    commandField.placeholder = '请输入自定义命令，例如：ls -la /home';
                } else {
                    commandField.placeholder = '系统已自动填写命令，你可以根据需要修改路径和参数';
                }
            }
        }

        // 更新时间选项显示
        function updateTimeOptions() {
            const frequency = document.getElementById('frequency').value;

            // 隐藏所有选项
            document.getElementById('once-options').style.display = 'none';
            document.getElementById('minute-options').style.display = 'none';
            document.getElementById('hour-options').style.display = 'none';
            document.getElementById('day-options').style.display = 'none';
            document.getElementById('week-options').style.display = 'none';
            document.getElementById('month-options').style.display = 'none';
            document.getElementById('time-preview').style.display = 'none';

            // 显示对应的选项
            if (frequency) {
                document.getElementById(frequency + '-options').style.display = 'block';
                updateCronExpression();
            }
        }

        // 更新Cron表达式
        function updateCronExpression() {
            const frequency = document.getElementById('frequency').value;
            let cronExpression = '';
            let previewText = '';

            switch (frequency) {
                case 'once':
                    const timing = document.getElementById('once-timing').value;
                    if (timing === 'immediate') {
                        cronExpression = '@once';
                        previewText = '创建后立即执行一次，然后自动禁用';
                    } else {
                        const date = document.getElementById('once-date').value;
                        const time = document.getElementById('once-time').value;
                        if (date && time) {
                            const [year, month, day] = date.split('-');
                            const [hour, minute] = time.split(':');
                            cronExpression = `@once:${year}-${month}-${day} ${hour}:${minute}`;
                            previewText = `将在 ${year}年${month}月${day}日 ${hour}:${minute} 执行一次，然后自动禁用`;
                        } else {
                            cronExpression = '@once';
                            previewText = '请选择执行日期和时间';
                        }
                    }
                    break;

                case 'minute':
                    const interval = document.getElementById('minute-interval').value;
                    cronExpression = `*/${interval} * * * *`;
                    previewText = `每${interval}分钟执行一次`;
                    break;

                case 'hour':
                    const hourMinute = document.getElementById('hour-minute').value;
                    cronExpression = `${hourMinute} * * * *`;
                    previewText = `每小时的第${hourMinute}分钟执行`;
                    break;

                case 'day':
                    const dayHour = document.getElementById('day-hour').value;
                    const dayMinute = document.getElementById('day-minute').value;
                    cronExpression = `${dayMinute} ${dayHour} * * *`;
                    previewText = `每天${String(dayHour).padStart(2, '0')}:${String(dayMinute).padStart(2, '0')}执行`;
                    break;

                case 'week':
                    const weekDay = document.getElementById('week-day').value;
                    const weekHour = document.getElementById('week-hour').value;
                    const weekMinute = document.getElementById('week-minute').value;
                    cronExpression = `${weekMinute} ${weekHour} * * ${weekDay}`;
                    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                    previewText = `每${weekDays[weekDay]} ${String(weekHour).padStart(2, '0')}:${String(weekMinute).padStart(2, '0')}执行`;
                    break;

                case 'month':
                    const monthDay = document.getElementById('month-day').value;
                    const monthHour = document.getElementById('month-hour').value;
                    const monthMinute = document.getElementById('month-minute').value;
                    cronExpression = `${monthMinute} ${monthHour} ${monthDay} * *`;
                    previewText = `每月${monthDay}号 ${String(monthHour).padStart(2, '0')}:${String(monthMinute).padStart(2, '0')}执行`;
                    break;
            }

            if (cronExpression) {
                document.getElementById('cron_expression').value = cronExpression;
                document.getElementById('preview-text').textContent = previewText;
                document.getElementById('time-preview').style.display = 'block';
            }
        }

        // 为所有时间选择器添加事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 分钟间隔
            document.getElementById('minute-interval').addEventListener('change', updateCronExpression);

            // 小时设置
            document.getElementById('hour-minute').addEventListener('change', updateCronExpression);

            // 每天设置
            document.getElementById('day-hour').addEventListener('change', updateCronExpression);
            document.getElementById('day-minute').addEventListener('change', updateCronExpression);

            // 每周设置
            document.getElementById('week-day').addEventListener('change', updateCronExpression);
            document.getElementById('week-hour').addEventListener('change', updateCronExpression);
            document.getElementById('week-minute').addEventListener('change', updateCronExpression);

            // 每月设置
            document.getElementById('month-day').addEventListener('change', updateCronExpression);
            document.getElementById('month-hour').addEventListener('change', updateCronExpression);
            document.getElementById('month-minute').addEventListener('change', updateCronExpression);
        });

        // 显示消息
        function showMessage(message, type = 'success') {
            const alertElement = document.getElementById(type === 'success' ? 'success-alert' : 'error-alert');
            alertElement.textContent = message;
            alertElement.style.display = 'block';

            // 滚动到消息位置
            alertElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        // AJAX请求
        function sendRequest(data) {
            return fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(data)
            }).then(response => response.json());
        }

        // 添加任务
        document.getElementById('task-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'add_task');

            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ 创建中...';
            submitBtn.disabled = true;

            sendRequest(formData).then(result => {
                if (result.success) {
                    showMessage('🎉 任务创建成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('❌ ' + result.message, 'error');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            }).catch(error => {
                showMessage('❌ 网络错误，请重试', 'error');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        // 编辑任务
        document.getElementById('edit-task-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'edit_task');

            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ 保存中...';
            submitBtn.disabled = true;

            sendRequest(formData).then(result => {
                if (result.success) {
                    showMessage('🎉 任务修改成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('❌ ' + result.message, 'error');
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            }).catch(error => {
                showMessage('❌ 网络错误，请重试', 'error');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });

        // 删除任务
        function deleteTask(id) {
            if (confirm('🗑️ 确定要删除这个任务吗？\n删除后无法恢复！')) {
                sendRequest({action: 'delete_task', id: id}).then(result => {
                    if (result.success) {
                        showMessage('🗑️ 任务删除成功！');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage('❌ ' + result.message, 'error');
                    }
                });
            }
        }

        // 切换任务状态
        function toggleTask(id) {
            sendRequest({action: 'toggle_task', id: id}).then(result => {
                if (result.success) {
                    showMessage('✅ 任务状态更新成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage('❌ ' + result.message, 'error');
                }
            });
        }

        // 执行任务
        function runTask(id) {
            if (confirm('▶️ 确定要立即执行这个任务吗？')) {
                showMessage('⏳ 任务执行中，请稍候...');

                sendRequest({action: 'run_task', id: id}).then(result => {
                    if (result.success) {
                        const output = result.output ? result.output.substring(0, 200) : '无输出';
                        showMessage('🎉 任务执行成功！\n输出: ' + output);
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showMessage('❌ 任务执行失败: ' + result.message, 'error');
                    }
                });
            }
        }

        // 编辑任务
        function editTask(id) {
            // 获取任务数据
            sendRequest({action: 'get_task', id: id}).then(result => {
                if (result.success) {
                    const task = result.task;

                    // 填充编辑表单
                    document.getElementById('edit-task-id').value = task.id;
                    document.getElementById('edit-name').value = task.name;
                    document.getElementById('edit-command').value = task.command;
                    document.getElementById('edit-cron').value = task.cron_expression;

                    // 显示编辑表单
                    showEditForm();
                } else {
                    showMessage('❌ ' + result.message, 'error');
                }
            });
        }

        // 执行所有启用的任务
        function runAllTasks() {
            if (confirm('▶️ 确定要执行所有启用的任务吗？')) {
                showMessage('⏳ 正在执行所有任务，请稍候...');
                // 这里可以添加执行所有任务的逻辑
                setTimeout(() => {
                    showMessage('🎉 所有任务执行完成！');
                    location.reload();
                }, 3000);
            }
        }

        // 切换输出显示
        function toggleOutput(logId) {
            const shortElement = document.getElementById('short-' + logId);
            const fullElement = document.getElementById('full-' + logId);
            const btnElement = document.getElementById('btn-' + logId);

            if (fullElement.style.display === 'none') {
                // 显示完整输出
                shortElement.style.display = 'none';
                fullElement.style.display = 'inline';
                btnElement.textContent = '📖 收起输出';
            } else {
                // 显示简短输出
                shortElement.style.display = 'inline';
                fullElement.style.display = 'none';
                btnElement.textContent = '📖 查看完整输出';
            }
        }

        // 切换命令显示
        function toggleCommand(taskId) {
            const shortElement = document.getElementById('cmd-short-' + taskId);
            const fullElement = document.getElementById('cmd-full-' + taskId);
            const btnElement = document.getElementById('cmd-btn-' + taskId);

            if (fullElement.style.display === 'none') {
                // 显示完整命令
                shortElement.style.display = 'none';
                fullElement.style.display = 'inline';
                btnElement.textContent = '📖 收起命令';
            } else {
                // 显示简短命令
                shortElement.style.display = 'inline';
                fullElement.style.display = 'none';
                btnElement.textContent = '📖 查看完整命令';
            }
        }
    </script>
</body>
</html>
