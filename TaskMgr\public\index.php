<?php
/**
 * 主入口文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 自动加载类文件
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/../src/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 加载配置
$config = require __DIR__ . '/../config/app.php';

// 设置时区
date_default_timezone_set($config['timezone']);

// 路由处理
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];
$base_path = dirname($script_name);

// 移除基础路径
if ($base_path !== '/') {
    $request_uri = substr($request_uri, strlen($base_path));
}

// 移除查询字符串
$path = parse_url($request_uri, PHP_URL_PATH);

// 简单路由
switch ($path) {
    case '/':
    case '/index.php':
        require __DIR__ . '/views/dashboard.php';
        break;
    case '/tasks':
        require __DIR__ . '/views/tasks.php';
        break;
    case '/logs':
        require __DIR__ . '/views/logs.php';
        break;
    case '/api/tasks':
        require __DIR__ . '/api/tasks.php';
        break;
    case '/api/logs':
        require __DIR__ . '/api/logs.php';
        break;
    case '/api/execute':
        require __DIR__ . '/api/execute.php';
        break;
    default:
        http_response_code(404);
        echo "页面未找到";
        break;
}
