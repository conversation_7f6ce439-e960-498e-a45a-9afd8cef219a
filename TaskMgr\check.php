#!/usr/bin/env php
<?php
/**
 * TaskMgr 系统检查脚本
 * 检查系统环境和配置是否正确
 */

echo "TaskMgr 系统检查\n";
echo "================\n\n";

$errors = [];
$warnings = [];
$success = [];

// 检查PHP版本
echo "1. 检查PHP版本...\n";
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    $success[] = "PHP版本: " . PHP_VERSION . " ✓";
} else {
    $errors[] = "PHP版本过低: " . PHP_VERSION . " (需要 >= 7.4.0)";
}

// 检查PHP扩展
echo "2. 检查PHP扩展...\n";
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $success[] = "PHP扩展 $ext ✓";
    } else {
        $errors[] = "缺少PHP扩展: $ext";
    }
}

// 检查目录权限
echo "3. 检查目录权限...\n";
$directories = [
    'logs' => 0755,
    'config' => 0755,
    'public' => 0755,
    'src' => 0755
];

foreach ($directories as $dir => $expectedPerm) {
    if (is_dir($dir)) {
        $actualPerm = fileperms($dir) & 0777;
        if ($actualPerm >= $expectedPerm) {
            $success[] = "目录 $dir 权限正确 (" . decoct($actualPerm) . ") ✓";
        } else {
            $warnings[] = "目录 $dir 权限可能不足 (" . decoct($actualPerm) . ")";
        }
        
        if (is_writable($dir)) {
            $success[] = "目录 $dir 可写 ✓";
        } else {
            $errors[] = "目录 $dir 不可写";
        }
    } else {
        $errors[] = "目录 $dir 不存在";
    }
}

// 检查配置文件
echo "4. 检查配置文件...\n";
$configFiles = ['config/database.php', 'config/app.php'];
foreach ($configFiles as $file) {
    if (file_exists($file)) {
        $success[] = "配置文件 $file 存在 ✓";
        
        if (is_readable($file)) {
            $success[] = "配置文件 $file 可读 ✓";
        } else {
            $errors[] = "配置文件 $file 不可读";
        }
    } else {
        $errors[] = "配置文件 $file 不存在";
    }
}

// 检查数据库连接
echo "5. 检查数据库连接...\n";
if (file_exists('config/database.php')) {
    try {
        $dbConfig = require 'config/database.php';
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
        $success[] = "数据库连接成功 ✓";
        
        // 检查数据表
        $tables = ['taskmgr_tasks', 'taskmgr_task_logs', 'taskmgr_config', 'taskmgr_task_locks'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $success[] = "数据表 $table 存在 ✓";
            } else {
                $errors[] = "数据表 $table 不存在";
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "数据库连接失败: " . $e->getMessage();
    }
} else {
    $errors[] = "数据库配置文件不存在";
}

// 检查核心文件
echo "6. 检查核心文件...\n";
$coreFiles = [
    'src/Database.php',
    'src/Task.php',
    'src/CronParser.php',
    'src/TaskExecutor.php',
    'src/Logger.php',
    'src/Security.php',
    'public/index.php',
    'cron.php'
];

foreach ($coreFiles as $file) {
    if (file_exists($file)) {
        $success[] = "核心文件 $file 存在 ✓";
    } else {
        $errors[] = "核心文件 $file 不存在";
    }
}

// 检查Web服务器配置
echo "7. 检查Web服务器配置...\n";
if (isset($_SERVER['SERVER_SOFTWARE'])) {
    $success[] = "Web服务器: " . $_SERVER['SERVER_SOFTWARE'] . " ✓";
} else {
    $warnings[] = "无法检测Web服务器信息（可能在CLI模式下运行）";
}

if (file_exists('public/.htaccess')) {
    $success[] = "Apache重写规则文件存在 ✓";
} else {
    $warnings[] = "Apache重写规则文件不存在（如果使用Nginx可忽略）";
}

// 检查Cron配置
echo "8. 检查Cron配置...\n";
if (file_exists('cron.php')) {
    if (is_executable('cron.php')) {
        $success[] = "Cron脚本可执行 ✓";
    } else {
        $warnings[] = "Cron脚本不可执行，请运行: chmod +x cron.php";
    }
} else {
    $errors[] = "Cron脚本不存在";
}

// 检查系统资源
echo "9. 检查系统资源...\n";
$memoryLimit = ini_get('memory_limit');
$memoryLimitBytes = return_bytes($memoryLimit);
if ($memoryLimitBytes >= 128 * 1024 * 1024) { // 128MB
    $success[] = "PHP内存限制: $memoryLimit ✓";
} else {
    $warnings[] = "PHP内存限制较低: $memoryLimit (建议 >= 128M)";
}

$maxExecutionTime = ini_get('max_execution_time');
if ($maxExecutionTime >= 300 || $maxExecutionTime == 0) {
    $success[] = "PHP最大执行时间: $maxExecutionTime ✓";
} else {
    $warnings[] = "PHP最大执行时间较短: $maxExecutionTime (建议 >= 300)";
}

// 检查时区设置
echo "10. 检查时区设置...\n";
$timezone = date_default_timezone_get();
if ($timezone) {
    $success[] = "时区设置: $timezone ✓";
} else {
    $warnings[] = "时区未设置";
}

// 输出结果
echo "\n检查结果\n";
echo "========\n\n";

if (!empty($success)) {
    echo "✓ 成功项目:\n";
    foreach ($success as $item) {
        echo "  - $item\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠ 警告项目:\n";
    foreach ($warnings as $item) {
        echo "  - $item\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "✗ 错误项目:\n";
    foreach ($errors as $item) {
        echo "  - $item\n";
    }
    echo "\n";
}

// 总结
echo "总结\n";
echo "----\n";
echo "成功: " . count($success) . " 项\n";
echo "警告: " . count($warnings) . " 项\n";
echo "错误: " . count($errors) . " 项\n\n";

if (empty($errors)) {
    if (empty($warnings)) {
        echo "🎉 系统检查完全通过！TaskMgr已准备就绪。\n";
        exit(0);
    } else {
        echo "✅ 系统基本正常，但有一些警告项目需要注意。\n";
        exit(0);
    }
} else {
    echo "❌ 系统检查发现错误，请修复后重新检查。\n";
    exit(1);
}

/**
 * 转换内存大小字符串为字节数
 */
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    
    return $val;
}
