<?php
require_once '../../conn_waf.php';

// 计算明日日期
$tomorrow = date('Y-m-d', strtotime('+1 day'));
// 查询明日值班安排
$sql = "SELECT a_com, b_com FROM 6_duty_sched WHERE sched_date = '$tomorrow'";
$result = mysqli_query($conn, $sql);
$dutySchedule = mysqli_fetch_assoc($result);

if (!$dutySchedule) {
    die("未找到明日值班安排记录\n");
}
// 收集用户ID
$userIds = array_filter([$dutySchedule['a_com'], $dutySchedule['b_com']]);
if (empty($userIds)) {
    die("值班安排中没有指定人员\n");
}

// 查询用户信息
$userIdList = implode(',', $userIds);
$sql = "SELECT name, phone FROM 3_user WHERE id IN ($userIdList)";
$result = mysqli_query($conn, $sql);

$mobileNumbers = [];
while ($user = mysqli_fetch_assoc($result)) {
    if (!in_array($user['name'], ['钟朝生', '谢军']) && !empty($user['phone'])) {
        $mobileNumbers[] = $user['phone'];
        echo "通知用户：{$user['name']} - {$user['phone']}\n";
    }
}

if (empty($mobileNumbers)) {
    die("没有需要发送短信的用户\n");
}

// 发送短信
$allMobiles = implode(',', $mobileNumbers);
$content = "您明日需要前往110指挥中心值班（指挥长），请准时到岗。";
$fromUserName = "岳池县公安局";

$postData = [
    'toUserMobile' => $allMobiles,
    'content' => $content,
    'fromUserName' => $fromUserName
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://10.152.110.168/api/SmsSystem.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

echo "短信发送完成，通知了 " . count($mobileNumbers) . " 个用户\n";
echo "响应：$response\n";
?>
