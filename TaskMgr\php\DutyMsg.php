<?php
/**
 * 值班通知短信发送脚本
 * 功能：查询明日值班人员并发送短信通知
 */

// 引入数据库连接配置文件
require_once '../conn_waf.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

/**
 * 记录日志
 */
function writeLog($message) {
    $logFile = __DIR__ . '/duty_msg.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * 发送短信
 */
function sendSMS($mobile, $content, $fromUserName) {
    $smsApiPath = '../../api/SmsSystem.php';
    
    // 检查短信接口文件是否存在
    if (!file_exists($smsApiPath)) {
        writeLog("错误：短信接口文件不存在 - $smsApiPath");
        return false;
    }
    
    // 准备POST数据
    $postData = [
        'toUserMobile' => $mobile,
        'content' => $content,
        'fromUserName' => $fromUserName
    ];
    
    // 使用cURL发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $smsApiPath);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        writeLog("cURL错误：$error");
        return false;
    }
    
    if ($httpCode !== 200) {
        writeLog("HTTP错误：状态码 $httpCode");
        return false;
    }
    
    writeLog("短信发送响应：$response");
    return true;
}

try {
    writeLog("开始执行值班通知脚本");
    
    // 计算明日日期
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    writeLog("查询明日值班安排，日期：$tomorrow");
    
    // 查询明日值班安排
    $sql = "SELECT a_com, b_com FROM 6_duty_sched WHERE sched_date = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$tomorrow]);
    $dutySchedule = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$dutySchedule) {
        writeLog("未找到明日值班安排记录");
        exit("未找到明日值班安排记录\n");
    }
    
    writeLog("找到值班安排：a_com={$dutySchedule['a_com']}, b_com={$dutySchedule['b_com']}");
    
    // 收集需要通知的用户ID
    $userIds = [];
    if (!empty($dutySchedule['a_com'])) {
        $userIds[] = $dutySchedule['a_com'];
    }
    if (!empty($dutySchedule['b_com'])) {
        $userIds[] = $dutySchedule['b_com'];
    }
    
    if (empty($userIds)) {
        writeLog("值班安排中没有指定人员");
        exit("值班安排中没有指定人员\n");
    }
    
    // 查询用户信息
    $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
    $sql = "SELECT id, name, phone FROM 3_user WHERE id IN ($placeholders)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($userIds);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        writeLog("未找到对应的用户信息");
        exit("未找到对应的用户信息\n");
    }
    
    writeLog("找到 " . count($users) . " 个用户");
    
    // 筛选需要发送短信的用户（排除钟朝生和谢军）
    $excludeNames = ['钟朝生', '谢军'];
    $notifyUsers = [];
    
    foreach ($users as $user) {
        if (!in_array($user['name'], $excludeNames)) {
            if (!empty($user['phone'])) {
                $notifyUsers[] = $user;
                writeLog("需要通知用户：{$user['name']} - {$user['phone']}");
            } else {
                writeLog("用户 {$user['name']} 没有手机号码，跳过");
            }
        } else {
            writeLog("用户 {$user['name']} 在排除列表中，跳过");
        }
    }
    
    if (empty($notifyUsers)) {
        writeLog("没有需要发送短信的用户");
        exit("没有需要发送短信的用户\n");
    }
    
    // 准备短信内容
    $smsContent = "您明日需要前往110指挥中心值班（指挥长），请准时到岗。";
    $fromUserName = "岳池县公安局";
    
    // 收集所有手机号码
    $mobileNumbers = [];
    foreach ($notifyUsers as $user) {
        $mobileNumbers[] = $user['phone'];
    }
    $allMobiles = implode(',', $mobileNumbers);
    
    writeLog("准备发送短信到：$allMobiles");
    writeLog("短信内容：$smsContent");
    
    // 发送短信
    $result = sendSMS($allMobiles, $smsContent, $fromUserName);
    
    if ($result) {
        writeLog("短信发送成功");
        echo "短信发送成功，通知了 " . count($notifyUsers) . " 个用户\n";
        
        // 输出通知的用户列表
        foreach ($notifyUsers as $user) {
            echo "- {$user['name']} ({$user['phone']})\n";
        }
    } else {
        writeLog("短信发送失败");
        echo "短信发送失败\n";
    }
    
} catch (Exception $e) {
    $errorMsg = "脚本执行出错：" . $e->getMessage();
    writeLog($errorMsg);
    echo $errorMsg . "\n";
    exit(1);
}

writeLog("脚本执行完成");
echo "脚本执行完成\n";
?>
