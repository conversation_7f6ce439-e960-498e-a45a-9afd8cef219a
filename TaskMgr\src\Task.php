<?php
/**
 * 任务模型类
 */

class Task {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取所有任务
     */
    public function getAllTasks($status = null, $limit = null, $offset = 0) {
        $sql = "SELECT * FROM taskmgr_tasks";
        $params = [];
        
        if ($status) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 根据ID获取任务
     */
    public function getTaskById($id) {
        $sql = "SELECT * FROM taskmgr_tasks WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * 创建新任务
     */
    public function createTask($data) {
        $sql = "INSERT INTO taskmgr_tasks (name, description, command, cron_expression, status, timeout_seconds, max_retries, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['name'],
            $data['description'] ?? '',
            $data['command'],
            $data['cron_expression'],
            $data['status'] ?? 'enabled',
            $data['timeout_seconds'] ?? 300,
            $data['max_retries'] ?? 3,
            $data['created_by'] ?? 'system'
        ];
        
        $taskId = $this->db->insert($sql, $params);
        
        // 计算下次执行时间
        $this->updateNextRunTime($taskId);
        
        return $taskId;
    }
    
    /**
     * 更新任务
     */
    public function updateTask($id, $data) {
        $fields = [];
        $params = [];
        
        $allowedFields = ['name', 'description', 'command', 'cron_expression', 'status', 'timeout_seconds', 'max_retries'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $params[] = $id;
        $sql = "UPDATE taskmgr_tasks SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $result = $this->db->execute($sql, $params);
        
        // 如果更新了cron表达式，重新计算下次执行时间
        if (isset($data['cron_expression'])) {
            $this->updateNextRunTime($id);
        }
        
        return $result > 0;
    }
    
    /**
     * 删除任务
     */
    public function deleteTask($id) {
        $sql = "DELETE FROM taskmgr_tasks WHERE id = ?";
        return $this->db->execute($sql, [$id]) > 0;
    }
    
    /**
     * 更新任务状态
     */
    public function updateTaskStatus($id, $status) {
        $sql = "UPDATE taskmgr_tasks SET status = ? WHERE id = ?";
        return $this->db->execute($sql, [$status, $id]) > 0;
    }
    
    /**
     * 更新下次执行时间
     */
    public function updateNextRunTime($id) {
        $task = $this->getTaskById($id);
        if (!$task) {
            return false;
        }
        
        $cronParser = new CronParser();
        $nextRunTime = $cronParser->getNextRunTime($task['cron_expression']);
        
        if ($nextRunTime) {
            $sql = "UPDATE taskmgr_tasks SET next_run_time = ? WHERE id = ?";
            return $this->db->execute($sql, [$nextRunTime, $id]) > 0;
        }
        
        return false;
    }
    
    /**
     * 获取需要执行的任务
     */
    public function getTasksToRun() {
        $sql = "SELECT * FROM taskmgr_tasks 
                WHERE status = 'enabled' 
                AND (next_run_time IS NULL OR next_run_time <= NOW())
                AND id NOT IN (SELECT task_id FROM taskmgr_task_locks WHERE expires_at > NOW())
                ORDER BY next_run_time ASC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * 更新任务执行统计
     */
    public function updateTaskStats($id, $success = true) {
        if ($success) {
            $sql = "UPDATE taskmgr_tasks SET 
                    run_count = run_count + 1, 
                    success_count = success_count + 1,
                    last_run_time = NOW(),
                    retry_count = 0
                    WHERE id = ?";
        } else {
            $sql = "UPDATE taskmgr_tasks SET 
                    run_count = run_count + 1, 
                    fail_count = fail_count + 1,
                    last_run_time = NOW(),
                    retry_count = retry_count + 1
                    WHERE id = ?";
        }
        
        return $this->db->execute($sql, [$id]) > 0;
    }
    
    /**
     * 获取任务统计信息
     */
    public function getTaskStats() {
        $sql = "SELECT 
                COUNT(*) as total_tasks,
                SUM(CASE WHEN status = 'enabled' THEN 1 ELSE 0 END) as enabled_tasks,
                SUM(CASE WHEN status = 'disabled' THEN 1 ELSE 0 END) as disabled_tasks,
                SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_tasks,
                SUM(run_count) as total_runs,
                SUM(success_count) as total_success,
                SUM(fail_count) as total_failures
                FROM taskmgr_tasks";
        
        return $this->db->fetch($sql);
    }
}
