<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskMgr - 计划任务管理器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .card-stats {
            transition: transform 0.2s;
        }
        .card-stats:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .task-item {
            border-left: 4px solid #dee2e6;
        }
        .task-item.enabled {
            border-left-color: #198754;
        }
        .task-item.disabled {
            border-left-color: #6c757d;
        }
        .task-item.running {
            border-left-color: #0d6efd;
        }
        .task-item.failed {
            border-left-color: #dc3545;
        }
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 4px;
        }
        .log-entry.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log-entry.warning {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-clock-history"></i> TaskMgr
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/tasks">任务管理</a>
                <a class="nav-link" href="/logs">日志查看</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-stats bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">总任务数</h5>
                                <h2 id="total-tasks">-</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-list-task fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">启用任务</h5>
                                <h2 id="enabled-tasks">-</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-play-circle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">运行中</h5>
                                <h2 id="running-tasks">-</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-arrow-repeat fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">今日执行</h5>
                                <h2 id="today-executions">-</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-calendar-check fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 最近任务 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">最近任务</h5>
                        <a href="/tasks" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus"></i> 新建任务
                        </a>
                    </div>
                    <div class="card-body">
                        <div id="recent-tasks">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近日志 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">最近日志</h5>
                        <a href="/logs" class="btn btn-sm btn-outline-primary">查看更多</a>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="recent-logs">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('/api/tasks?stats=1');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('total-tasks').textContent = data.data.total_tasks || 0;
                    document.getElementById('enabled-tasks').textContent = data.data.enabled_tasks || 0;
                    document.getElementById('running-tasks').textContent = data.data.running_tasks || 0;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 加载今日执行统计
        async function loadTodayStats() {
            try {
                const today = new Date().toISOString().split('T')[0];
                const response = await fetch(`/api/logs/log-stats?date=${today}`);
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('today-executions').textContent = 
                        data.data.task_executions.total_executions || 0;
                }
            } catch (error) {
                console.error('加载今日统计失败:', error);
            }
        }

        // 加载最近任务
        async function loadRecentTasks() {
            try {
                const response = await fetch('/api/tasks?limit=5');
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('recent-tasks');
                    
                    if (data.data.length === 0) {
                        container.innerHTML = '<p class="text-muted">暂无任务</p>';
                        return;
                    }
                    
                    container.innerHTML = data.data.map(task => `
                        <div class="task-item ${task.status} p-3 mb-2 border rounded">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">${task.name}</h6>
                                    <p class="mb-1 text-muted small">${task.description || '无描述'}</p>
                                    <small class="text-muted">
                                        <i class="bi bi-clock"></i> ${task.cron_expression}
                                        ${task.next_run_time ? `| 下次执行: ${new Date(task.next_run_time).toLocaleString()}` : ''}
                                    </small>
                                </div>
                                <span class="badge status-badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span>
                            </div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载最近任务失败:', error);
                document.getElementById('recent-tasks').innerHTML = '<p class="text-danger">加载失败</p>';
            }
        }

        // 加载最近日志
        async function loadRecentLogs() {
            try {
                const response = await fetch('/api/logs/recent-errors?limit=10');
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('recent-logs');
                    
                    if (data.data.length === 0) {
                        container.innerHTML = '<p class="text-muted small">暂无错误日志</p>';
                        return;
                    }
                    
                    container.innerHTML = data.data.map(log => `
                        <div class="log-entry ${log.status === 'failed' ? 'error' : ''}">
                            <div class="small">
                                <strong>${log.task_name}</strong>
                                <span class="float-end">${new Date(log.start_time).toLocaleString()}</span>
                            </div>
                            <div class="small text-muted">${log.error_message || '执行失败'}</div>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('加载最近日志失败:', error);
                document.getElementById('recent-logs').innerHTML = '<p class="text-danger small">加载失败</p>';
            }
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'enabled': 'success',
                'disabled': 'secondary',
                'running': 'primary',
                'completed': 'info',
                'failed': 'danger'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'enabled': '启用',
                'disabled': '禁用',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败'
            };
            return texts[status] || status;
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadTodayStats();
            loadRecentTasks();
            loadRecentLogs();

            // 每30秒刷新一次数据
            setInterval(() => {
                loadStats();
                loadTodayStats();
            }, 30000);
        });
    </script>
</body>
</html>
