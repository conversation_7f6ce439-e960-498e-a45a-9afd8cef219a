<?php
/**
 * Web版本的Cron设置工具
 */

$message = '';
$messageType = '';

// 处理设置请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['setup_cron'])) {
        $phpPath = $_POST['php_path'] ?? '';
        $indexPath = __FILE__;
        $indexPath = str_replace('setup_web.php', 'index.php', $indexPath);
        
        if (empty($phpPath)) {
            $message = "请选择PHP路径";
            $messageType = "error";
        } else {
            // 测试PHP路径
            $testCommand = "$phpPath -v 2>&1";
            $testOutput = shell_exec($testCommand);
            
            if (strpos($testOutput, 'PHP') !== false) {
                // PHP路径有效，设置Cron
                $cronCommand = "* * * * * $phpPath $indexPath cron >/dev/null 2>&1";
                
                // 获取现有crontab
                $currentCron = shell_exec('crontab -l 2>/dev/null') ?: '';
                
                // 移除旧的TaskMgr设置
                $lines = explode("\n", $currentCron);
                $newLines = [];
                foreach ($lines as $line) {
                    if (strpos($line, 'index.php') === false || strpos($line, '#') === 0) {
                        $newLines[] = $line;
                    }
                }
                
                // 添加新的TaskMgr设置
                $newLines[] = '';
                $newLines[] = '# TaskMgr 计划任务管理器';
                $newLines[] = $cronCommand;
                
                // 写入临时文件
                $tempFile = tempnam(sys_get_temp_dir(), 'crontab');
                file_put_contents($tempFile, implode("\n", $newLines));
                
                // 安装新的crontab
                $installCommand = "crontab $tempFile 2>&1";
                $installOutput = shell_exec($installCommand);
                
                unlink($tempFile);
                
                if (empty($installOutput) || strpos($installOutput, 'error') === false) {
                    $message = "✅ Cron任务设置成功！";
                    $messageType = "success";
                } else {
                    $message = "❌ Cron设置失败: " . $installOutput;
                    $messageType = "error";
                }
            } else {
                $message = "❌ PHP路径无效: " . $testOutput;
                $messageType = "error";
            }
        }
    }
}

// 查找可用的PHP路径
function findPhpPaths() {
    $validPaths = [];

    // 首先尝试当前PHP路径
    $currentPhp = PHP_BINARY;
    if ($currentPhp && strpos($currentPhp, 'php') !== false) {
        $version = shell_exec("$currentPhp -v 2>/dev/null | head -n1");
        if ($version && strpos($version, 'PHP') !== false) {
            $validPaths[] = [
                'path' => $currentPhp,
                'version' => trim($version),
                'note' => '当前运行的PHP'
            ];
        }
    }

    // 尝试常见的命令
    $commands = ['php', 'php8', 'php7', 'php81', 'php80', 'php74', 'php73'];
    foreach ($commands as $cmd) {
        $output = shell_exec("which $cmd 2>/dev/null");
        if ($output) {
            $path = trim($output);
            if (!in_array($path, array_column($validPaths, 'path'))) {
                $version = shell_exec("$path -v 2>/dev/null | head -n1");
                if ($version && strpos($version, 'PHP') !== false) {
                    $validPaths[] = [
                        'path' => $path,
                        'version' => trim($version),
                        'note' => "通过 which $cmd 找到"
                    ];
                }
            }
        }
    }

    // 尝试使用whereis命令
    $whereisOutput = shell_exec('whereis php 2>/dev/null');
    if ($whereisOutput) {
        $parts = explode(' ', $whereisOutput);
        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, '/') === 0 && strpos($part, 'php') !== false) {
                if (!in_array($part, array_column($validPaths, 'path'))) {
                    // 由于open_basedir限制，我们不能直接检查文件，但可以尝试执行
                    $version = @shell_exec("$part -v 2>/dev/null | head -n1");
                    if ($version && strpos($version, 'PHP') !== false) {
                        $validPaths[] = [
                            'path' => $part,
                            'version' => trim($version),
                            'note' => '通过 whereis 找到'
                        ];
                    }
                }
            }
        }
    }

    // 如果还是没找到，提供一些常见路径让用户手动选择
    if (empty($validPaths)) {
        $commonPaths = [
            '/usr/bin/php',
            '/usr/local/bin/php',
            '/www/server/php/bin/php',
            '/www/server/php/81/bin/php',
            '/www/server/php/80/bin/php',
            '/www/server/php/74/bin/php'
        ];

        foreach ($commonPaths as $path) {
            // 尝试执行看是否有效
            $version = @shell_exec("$path -v 2>/dev/null | head -n1");
            if ($version && strpos($version, 'PHP') !== false) {
                $validPaths[] = [
                    'path' => $path,
                    'version' => trim($version),
                    'note' => '常见路径（未验证文件存在）'
                ];
            }
        }
    }

    return $validPaths;
}

$phpPaths = findPhpPaths();

// 检查当前Cron状态
$currentCron = shell_exec('crontab -l 2>/dev/null') ?: '';
$hasCron = strpos($currentCron, 'index.php') !== false;
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskMgr Cron设置</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 0;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        select, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .php-option {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🔧 TaskMgr Cron设置</h1>
        <p>通过Web界面设置系统Cron任务</p>
        <p><a href="index.php">← 返回主界面</a> | <a href="diagnose.php">问题诊断</a> | <a href="test_cron.php">Cron测试</a></p>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-<?= $messageType ?>">
        <?= $message ?>
    </div>
    <?php endif; ?>

    <!-- 当前状态 -->
    <div class="card">
        <h2>📋 当前Cron状态</h2>
        <?php if ($hasCron): ?>
        <div class="alert alert-success">
            ✅ 系统Cron已设置
            <pre><?= htmlspecialchars($currentCron) ?></pre>
        </div>
        <?php else: ?>
        <div class="alert alert-warning">
            ⚠️ 系统Cron未设置，定时任务不会自动执行
        </div>
        <?php endif; ?>
    </div>

    <!-- PHP路径检测 -->
    <div class="card">
        <h2>🐘 PHP路径检测</h2>
        <?php if (empty($phpPaths)): ?>
        <div class="alert alert-error">
            ❌ 未找到可用的PHP可执行文件
            <p>可能的原因：</p>
            <ul>
                <li>PHP未安装</li>
                <li>PHP不在常见路径中</li>
                <li>PHP文件没有执行权限</li>
            </ul>
        </div>
        <?php else: ?>
        <div class="alert alert-success">
            ✅ 找到 <?= count($phpPaths) ?> 个可用的PHP路径
        </div>
        
        <form method="POST">
            <h3>选择PHP路径：</h3>
            <?php foreach ($phpPaths as $i => $php): ?>
            <div class="php-option">
                <label>
                    <input type="radio" name="php_path" value="<?= htmlspecialchars($php['path']) ?>" <?= $i === 0 ? 'checked' : '' ?>>
                    <strong><?= htmlspecialchars($php['path']) ?></strong><br>
                    <small><?= htmlspecialchars($php['version']) ?></small>
                </label>
            </div>
            <?php endforeach; ?>
            
            <div style="margin-top: 20px;">
                <button type="submit" name="setup_cron">
                    🚀 设置系统Cron
                </button>
            </div>
        </form>
        <?php endif; ?>
    </div>

    <!-- 手动设置说明 -->
    <div class="card">
        <h2>📝 手动设置说明</h2>
        <p>如果自动设置失败，您可以手动设置：</p>
        
        <h3>1. 编辑crontab：</h3>
        <pre>crontab -e</pre>
        
        <h3>2. 添加以下行：</h3>
        <?php if (!empty($phpPaths)): ?>
        <pre>* * * * * <?= htmlspecialchars($phpPaths[0]['path']) ?> <?= str_replace('setup_web.php', 'index.php', __FILE__) ?> cron >/dev/null 2>&1</pre>
        <?php else: ?>
        <pre>* * * * * /usr/bin/php <?= str_replace('setup_web.php', 'index.php', __FILE__) ?> cron >/dev/null 2>&1</pre>
        <p><em>注意：请将 <code>/usr/bin/php</code> 替换为实际的PHP路径</em></p>
        <?php endif; ?>
        
        <h3>3. 保存并退出</h3>
        <p>在vi编辑器中：按 <code>Esc</code>，然后输入 <code>:wq</code> 并按回车</p>
    </div>

    <!-- 验证设置 -->
    <div class="card">
        <h2>✅ 验证设置</h2>
        <p>设置完成后，请：</p>
        <ol>
            <li>访问 <a href="test_cron.php">Cron测试页面</a> 创建测试任务</li>
            <li>等待1-2分钟查看是否执行</li>
            <li>检查 <a href="index.php">主界面</a> 的执行日志</li>
        </ol>
    </div>

    <div class="card">
        <p><strong>需要帮助？</strong></p>
        <p>
            <a href="diagnose.php">问题诊断</a> | 
            <a href="debug.php">调试工具</a> | 
            <a href="setup_guide.php">详细设置指南</a>
        </p>
    </div>
</body>
</html>
