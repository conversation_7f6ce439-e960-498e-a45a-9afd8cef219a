<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理 - TaskMgr</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .task-row {
            transition: background-color 0.2s;
        }
        .task-row:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .cron-help {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .modal-lg {
            max-width: 800px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-clock-history"></i> TaskMgr
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">仪表板</a>
                <a class="nav-link active" href="/tasks">任务管理</a>
                <a class="nav-link" href="/logs">日志查看</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>任务管理</h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#taskModal" onclick="openTaskModal()">
                <i class="bi bi-plus"></i> 新建任务
            </button>
        </div>

        <!-- 筛选器 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter" onchange="loadTasks()">
                            <option value="">所有状态</option>
                            <option value="enabled">启用</option>
                            <option value="disabled">禁用</option>
                            <option value="running">运行中</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索任务名称..." onkeyup="searchTasks()">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary" onclick="loadTasks()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>Cron表达式</th>
                                <th>状态</th>
                                <th>上次执行</th>
                                <th>下次执行</th>
                                <th>成功/失败</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tasksTableBody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务编辑模态框 -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taskModalTitle">新建任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <input type="hidden" id="taskId">
                        
                        <div class="mb-3">
                            <label for="taskName" class="form-label">任务名称 *</label>
                            <input type="text" class="form-control" id="taskName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="taskCommand" class="form-label">执行命令 *</label>
                            <textarea class="form-control" id="taskCommand" rows="3" required></textarea>
                            <div class="form-text">请输入要执行的shell命令</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="cronExpression" class="form-label">Cron表达式 *</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="cronExpression" required>
                                <button type="button" class="btn btn-outline-secondary" onclick="showCronHelp()">
                                    <i class="bi bi-question-circle"></i>
                                </button>
                            </div>
                            <div id="cronDescription" class="form-text"></div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timeoutSeconds" class="form-label">超时时间（秒）</label>
                                    <input type="number" class="form-control" id="timeoutSeconds" value="300" min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxRetries" class="form-label">最大重试次数</label>
                                    <input type="number" class="form-control" id="maxRetries" value="3" min="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="taskStatus" class="form-label">状态</label>
                            <select class="form-select" id="taskStatus">
                                <option value="enabled">启用</option>
                                <option value="disabled">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveTask()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cron帮助模态框 -->
    <div class="modal fade" id="cronHelpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cron表达式帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Cron表达式格式：<code>分钟 小时 日期 月份 星期</code></p>
                    
                    <h6>字段说明：</h6>
                    <ul>
                        <li>分钟：0-59</li>
                        <li>小时：0-23</li>
                        <li>日期：1-31</li>
                        <li>月份：1-12</li>
                        <li>星期：0-7（0和7都表示周日）</li>
                    </ul>
                    
                    <h6>特殊字符：</h6>
                    <ul>
                        <li><code>*</code> - 匹配任何值</li>
                        <li><code>,</code> - 分隔多个值</li>
                        <li><code>-</code> - 指定范围</li>
                        <li><code>/</code> - 指定步长</li>
                    </ul>
                    
                    <h6>常用示例：</h6>
                    <div id="cronExamples"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let tasks = [];
        let currentTaskId = null;

        // 加载任务列表
        async function loadTasks() {
            try {
                const status = document.getElementById('statusFilter').value;
                const url = status ? `/api/tasks?status=${status}` : '/api/tasks';
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    tasks = data.data;
                    renderTasks(tasks);
                } else {
                    showAlert('加载任务失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('加载任务失败:', error);
                showAlert('加载任务失败', 'danger');
            }
        }

        // 渲染任务列表
        function renderTasks(taskList) {
            const tbody = document.getElementById('tasksTableBody');
            
            if (taskList.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无任务</td></tr>';
                return;
            }
            
            tbody.innerHTML = taskList.map(task => `
                <tr class="task-row">
                    <td>
                        <strong>${task.name}</strong>
                        ${task.description ? `<br><small class="text-muted">${task.description}</small>` : ''}
                    </td>
                    <td><code>${task.cron_expression}</code></td>
                    <td><span class="badge status-badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span></td>
                    <td>${task.last_run_time ? new Date(task.last_run_time).toLocaleString() : '-'}</td>
                    <td>${task.next_run_time ? new Date(task.next_run_time).toLocaleString() : '-'}</td>
                    <td>
                        <span class="text-success">${task.success_count}</span> / 
                        <span class="text-danger">${task.fail_count}</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editTask(${task.id})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="executeTask(${task.id})" title="立即执行">
                                <i class="bi bi-play"></i>
                            </button>
                            <button class="btn btn-outline-${task.status === 'enabled' ? 'warning' : 'success'}" 
                                    onclick="toggleTaskStatus(${task.id}, '${task.status === 'enabled' ? 'disabled' : 'enabled'}')" 
                                    title="${task.status === 'enabled' ? '禁用' : '启用'}">
                                <i class="bi bi-${task.status === 'enabled' ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 搜索任务
        function searchTasks() {
            const keyword = document.getElementById('searchInput').value.toLowerCase();
            const filtered = tasks.filter(task => 
                task.name.toLowerCase().includes(keyword) || 
                (task.description && task.description.toLowerCase().includes(keyword))
            );
            renderTasks(filtered);
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'enabled': 'success',
                'disabled': 'secondary',
                'running': 'primary',
                'completed': 'info',
                'failed': 'danger'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'enabled': '启用',
                'disabled': '禁用',
                'running': '运行中',
                'completed': '已完成',
                'failed': '失败'
            };
            return texts[status] || status;
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 打开任务模态框
        function openTaskModal(taskId = null) {
            currentTaskId = taskId;
            const modal = document.getElementById('taskModal');
            const title = document.getElementById('taskModalTitle');
            const form = document.getElementById('taskForm');

            if (taskId) {
                title.textContent = '编辑任务';
                const task = tasks.find(t => t.id === taskId);
                if (task) {
                    document.getElementById('taskId').value = task.id;
                    document.getElementById('taskName').value = task.name;
                    document.getElementById('taskDescription').value = task.description || '';
                    document.getElementById('taskCommand').value = task.command;
                    document.getElementById('cronExpression').value = task.cron_expression;
                    document.getElementById('timeoutSeconds').value = task.timeout_seconds;
                    document.getElementById('maxRetries').value = task.max_retries;
                    document.getElementById('taskStatus').value = task.status;
                    validateCronExpression();
                }
            } else {
                title.textContent = '新建任务';
                form.reset();
                document.getElementById('taskId').value = '';
                document.getElementById('timeoutSeconds').value = '300';
                document.getElementById('maxRetries').value = '3';
                document.getElementById('taskStatus').value = 'enabled';
                document.getElementById('cronDescription').textContent = '';
            }
        }

        // 编辑任务
        function editTask(taskId) {
            openTaskModal(taskId);
            new bootstrap.Modal(document.getElementById('taskModal')).show();
        }

        // 保存任务
        async function saveTask() {
            const form = document.getElementById('taskForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const taskData = {
                name: document.getElementById('taskName').value,
                description: document.getElementById('taskDescription').value,
                command: document.getElementById('taskCommand').value,
                cron_expression: document.getElementById('cronExpression').value,
                timeout_seconds: parseInt(document.getElementById('timeoutSeconds').value),
                max_retries: parseInt(document.getElementById('maxRetries').value),
                status: document.getElementById('taskStatus').value
            };

            try {
                let response;
                if (currentTaskId) {
                    response = await fetch(`/api/tasks/${currentTaskId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(taskData)
                    });
                } else {
                    response = await fetch('/api/tasks', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(taskData)
                    });
                }

                const data = await response.json();
                if (data.success) {
                    showAlert(data.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                    loadTasks();
                } else {
                    showAlert(data.message, 'danger');
                }
            } catch (error) {
                console.error('保存任务失败:', error);
                showAlert('保存任务失败', 'danger');
            }
        }

        // 删除任务
        async function deleteTask(taskId) {
            if (!confirm('确定要删除这个任务吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/tasks/${taskId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();
                if (data.success) {
                    showAlert(data.message, 'success');
                    loadTasks();
                } else {
                    showAlert(data.message, 'danger');
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                showAlert('删除任务失败', 'danger');
            }
        }

        // 切换任务状态
        async function toggleTaskStatus(taskId, newStatus) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/status`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ status: newStatus })
                });

                const data = await response.json();
                if (data.success) {
                    showAlert(data.message, 'success');
                    loadTasks();
                } else {
                    showAlert(data.message, 'danger');
                }
            } catch (error) {
                console.error('更新状态失败:', error);
                showAlert('更新状态失败', 'danger');
            }
        }

        // 执行任务
        async function executeTask(taskId) {
            if (!confirm('确定要立即执行这个任务吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/execute/manual', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ task_id: taskId })
                });

                const data = await response.json();
                if (data.success) {
                    showAlert('任务执行完成', 'success');
                    loadTasks();
                } else {
                    showAlert('任务执行失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('执行任务失败:', error);
                showAlert('执行任务失败', 'danger');
            }
        }

        // 验证Cron表达式
        async function validateCronExpression() {
            const cronExpression = document.getElementById('cronExpression').value;
            if (!cronExpression) {
                document.getElementById('cronDescription').textContent = '';
                return;
            }

            try {
                const response = await fetch('/api/execute/validate-cron', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cron_expression: cronExpression })
                });

                const data = await response.json();
                if (data.success && data.data.valid) {
                    document.getElementById('cronDescription').textContent =
                        `${data.data.description} | 下次执行: ${new Date(data.data.next_run_time).toLocaleString()}`;
                    document.getElementById('cronDescription').className = 'form-text text-success';
                } else {
                    document.getElementById('cronDescription').textContent = 'Cron表达式格式错误';
                    document.getElementById('cronDescription').className = 'form-text text-danger';
                }
            } catch (error) {
                console.error('验证Cron表达式失败:', error);
            }
        }

        // 显示Cron帮助
        async function showCronHelp() {
            try {
                const response = await fetch('/api/execute/cron-examples');
                const data = await response.json();

                if (data.success) {
                    const examplesDiv = document.getElementById('cronExamples');
                    examplesDiv.innerHTML = Object.entries(data.data).map(([cron, desc]) =>
                        `<div class="mb-2">
                            <code>${cron}</code> - ${desc}
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="useCronExample('${cron}')">使用</button>
                        </div>`
                    ).join('');
                }
            } catch (error) {
                console.error('加载Cron示例失败:', error);
            }

            new bootstrap.Modal(document.getElementById('cronHelpModal')).show();
        }

        // 使用Cron示例
        function useCronExample(cronExpression) {
            document.getElementById('cronExpression').value = cronExpression;
            validateCronExpression();
            bootstrap.Modal.getInstance(document.getElementById('cronHelpModal')).hide();
        }

        // 监听Cron表达式输入
        document.addEventListener('DOMContentLoaded', function() {
            loadTasks();

            document.getElementById('cronExpression').addEventListener('input', validateCronExpression);
        });
    </script>
</body>
</html>
