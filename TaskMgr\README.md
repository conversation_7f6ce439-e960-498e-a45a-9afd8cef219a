# TaskMgr - 计划任务管理器

一个基于PHP开发的Web计划任务管理系统，专为openEuler 24操作系统优化。

## 功能特性

- ✅ **任务管理**: 创建、编辑、删除计划任务
- ✅ **Cron支持**: 完整的Cron表达式支持，灵活的时间调度
- ✅ **状态管理**: 任务启用/禁用/运行中/已完成状态管理
- ✅ **执行日志**: 详细的任务执行日志记录和查看
- ✅ **Web界面**: 直观的Web管理界面，无需登录即可使用
- ✅ **安全防护**: SQL注入防护、输入验证、XSS防护
- ✅ **实时监控**: 任务执行状态实时监控
- ✅ **错误处理**: 完善的错误处理和重试机制

## 系统要求

### 操作系统
- openEuler 24 (推荐)
- 其他Linux发行版 (兼容)

### 软件要求
- PHP 7.4+ (推荐 PHP 8.0+)
- MySQL 5.7+ 或 MariaDB 10.3+
- Web服务器 (Apache/Nginx)

### PHP扩展
- pdo
- pdo_mysql
- json
- mbstring
- curl

## 快速安装

### 1. 下载源码
```bash
git clone <repository-url> /var/www/TaskMgr
cd /var/www/TaskMgr
```

### 2. 设置权限
```bash
chmod +x install.php
chmod 755 logs public src config
chmod 600 config/database.php
```

### 3. 运行安装脚本
```bash
php install.php
```

安装脚本将引导您完成：
- 数据库配置
- 数据库表创建
- 权限设置
- Cron任务配置

### 4. 配置Web服务器

#### Apache配置
```apache
<VirtualHost *:80>
    ServerName taskmgr.example.com
    DocumentRoot /var/www/TaskMgr/public
    
    <Directory /var/www/TaskMgr/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog /var/log/httpd/taskmgr_error.log
    CustomLog /var/log/httpd/taskmgr_access.log combined
</VirtualHost>
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name taskmgr.example.com;
    root /var/www/TaskMgr/public;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.(ht|git) {
        deny all;
    }
}
```

### 5. 配置Cron任务
```bash
# 编辑crontab
crontab -e

# 添加以下行（每分钟检查一次）
* * * * * /usr/bin/php /var/www/TaskMgr/cron.php >> /var/www/TaskMgr/logs/cron.log 2>&1
```

## 目录结构

```
TaskMgr/
├── config/                 # 配置文件目录
│   ├── app.php            # 应用配置
│   └── database.php       # 数据库配置
├── database/              # 数据库相关
│   └── init.sql          # 数据库初始化脚本
├── logs/                  # 日志文件目录
├── public/                # Web根目录
│   ├── api/              # API接口
│   ├── views/            # 页面视图
│   ├── index.php         # 入口文件
│   └── .htaccess         # Apache重写规则
├── src/                   # 核心类库
│   ├── Database.php      # 数据库连接类
│   ├── Task.php          # 任务模型类
│   ├── CronParser.php    # Cron解析器
│   ├── TaskExecutor.php  # 任务执行器
│   ├── Logger.php        # 日志记录器
│   └── Security.php      # 安全防护类
├── cron.php              # Cron执行脚本
├── install.php           # 安装脚本
└── README.md             # 说明文档
```

## 使用说明

### 1. 访问Web界面
打开浏览器访问: `http://your-server/TaskMgr/public/`

### 2. 创建任务
1. 点击"新建任务"按钮
2. 填写任务信息：
   - 任务名称
   - 任务描述（可选）
   - 执行命令
   - Cron表达式
   - 超时时间
   - 重试次数
3. 点击"保存"

### 3. Cron表达式示例
```
* * * * *        # 每分钟执行
*/5 * * * *      # 每5分钟执行
0 * * * *        # 每小时执行
0 2 * * *        # 每天凌晨2点执行
0 0 * * 0        # 每周日午夜执行
0 0 1 * *        # 每月1号午夜执行
30 2 * * 1-5     # 工作日凌晨2:30执行
```

### 4. 管理任务
- **启用/禁用**: 切换任务状态
- **立即执行**: 手动触发任务执行
- **编辑**: 修改任务配置
- **删除**: 删除不需要的任务

### 5. 查看日志
- **任务执行日志**: 查看任务执行历史和结果
- **系统日志**: 查看系统运行日志
- **错误日志**: 查看执行失败的任务

## 配置说明

### 数据库配置 (config/database.php)
```php
return [
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'taskmgr',
    'username' => 'taskmgr_user',
    'password' => 'your_password',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
```

### 应用配置 (config/app.php)
```php
return [
    'app_name' => 'TaskMgr - 计划任务管理器',
    'timezone' => 'Asia/Shanghai',
    'debug' => false,
    
    'log' => [
        'level' => 'info',
        'max_files' => 30,
        'path' => __DIR__ . '/../logs/'
    ],
    
    'task' => [
        'max_execution_time' => 300,
        'concurrent_limit' => 5,
        'retry_attempts' => 3,
        'retry_delay' => 60
    ]
];
```

## 安全注意事项

1. **命令安全**: 系统会验证执行命令的安全性，禁止危险命令
2. **输入验证**: 所有用户输入都经过严格验证和清理
3. **SQL注入防护**: 使用预处理语句防止SQL注入
4. **访问控制**: 建议在生产环境中配置访问控制
5. **文件权限**: 确保敏感文件权限设置正确

## 故障排除

### 常见问题

1. **任务不执行**
   - 检查Cron服务是否运行: `systemctl status crond`
   - 检查crontab配置: `crontab -l`
   - 查看Cron日志: `tail -f logs/cron.log`

2. **数据库连接失败**
   - 检查数据库服务: `systemctl status mysqld`
   - 验证数据库配置: `config/database.php`
   - 检查用户权限

3. **Web界面无法访问**
   - 检查Web服务器状态
   - 验证虚拟主机配置
   - 检查文件权限

4. **日志文件过大**
   - 配置日志轮转
   - 定期清理旧日志
   - 调整日志级别

### 日志位置
- 应用日志: `logs/taskmgr_YYYY-MM-DD.log`
- Cron日志: `logs/cron.log`
- Web服务器日志: `/var/log/httpd/` 或 `/var/log/nginx/`

## 开发说明

### API接口

#### 任务管理
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建任务
- `PUT /api/tasks/{id}` - 更新任务
- `DELETE /api/tasks/{id}` - 删除任务

#### 日志查询
- `GET /api/logs/task-logs` - 获取任务执行日志
- `GET /api/logs/system-logs` - 获取系统日志

#### 任务执行
- `POST /api/execute/manual` - 手动执行任务
- `POST /api/execute/validate-cron` - 验证Cron表达式

### 扩展开发
系统采用模块化设计，可以轻松扩展功能：
- 添加新的任务类型
- 集成邮件通知
- 添加用户认证
- 集成监控系统

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 支持

如有问题或建议，请提交Issue或联系开发团队。
