# TaskMgr 部署指南

本文档详细说明如何在openEuler 24操作系统上部署TaskMgr计划任务管理器。

## 环境准备

### 1. 系统更新
```bash
# 更新系统包
sudo dnf update -y

# 安装基础工具
sudo dnf install -y wget curl git vim
```

### 2. 安装PHP
```bash
# 安装PHP和必需扩展
sudo dnf install -y php php-cli php-fpm php-mysql php-json php-mbstring php-curl

# 验证PHP版本
php --version

# 启动并启用PHP-FPM
sudo systemctl start php-fpm
sudo systemctl enable php-fpm
```

### 3. 安装MySQL/MariaDB
```bash
# 安装MariaDB
sudo dnf install -y mariadb-server mariadb

# 启动并启用MariaDB
sudo systemctl start mariadb
sudo systemctl enable mariadb

# 安全配置
sudo mysql_secure_installation
```

### 4. 安装Web服务器

#### 选项A: Apache
```bash
# 安装Apache
sudo dnf install -y httpd

# 启动并启用Apache
sudo systemctl start httpd
sudo systemctl enable httpd

# 配置防火墙
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

#### 选项B: Nginx
```bash
# 安装Nginx
sudo dnf install -y nginx

# 启动并启用Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 配置防火墙
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 数据库配置

### 1. 创建数据库和用户
```sql
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE taskmgr CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户
CREATE USER 'taskmgr_user'@'localhost' IDENTIFIED BY 'your_secure_password';

# 授权
GRANT ALL PRIVILEGES ON taskmgr.* TO 'taskmgr_user'@'localhost';

# 刷新权限
FLUSH PRIVILEGES;

# 退出
EXIT;
```

### 2. 优化数据库配置
编辑 `/etc/my.cnf.d/mariadb-server.cnf`:
```ini
[mysqld]
# 基础配置
max_connections = 200
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 时区配置
default-time-zone = '+08:00'

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mariadb/slow.log
long_query_time = 2
```

重启MariaDB:
```bash
sudo systemctl restart mariadb
```

## 应用部署

### 1. 下载和安装
```bash
# 创建项目目录
sudo mkdir -p /var/www/TaskMgr
cd /var/www/TaskMgr

# 下载源码（假设从Git仓库）
sudo git clone <repository-url> .

# 或者上传文件包并解压
# sudo tar -xzf TaskMgr.tar.gz

# 设置所有者
sudo chown -R apache:apache /var/www/TaskMgr  # Apache
# sudo chown -R nginx:nginx /var/www/TaskMgr   # Nginx

# 设置权限
sudo chmod -R 755 /var/www/TaskMgr
sudo chmod -R 755 /var/www/TaskMgr/logs
sudo chmod -R 755 /var/www/TaskMgr/config
sudo chmod 600 /var/www/TaskMgr/config/database.php
```

### 2. 运行安装脚本
```bash
cd /var/www/TaskMgr
sudo php install.php
```

按照提示输入数据库配置信息。

### 3. Web服务器配置

#### Apache配置
创建虚拟主机配置文件 `/etc/httpd/conf.d/taskmgr.conf`:
```apache
<VirtualHost *:80>
    ServerName taskmgr.yourdomain.com
    DocumentRoot /var/www/TaskMgr/public
    
    <Directory /var/www/TaskMgr/public>
        AllowOverride All
        Require all granted
        
        # 安全设置
        Options -Indexes -ExecCGI
        
        # PHP设置
        php_admin_value upload_max_filesize 10M
        php_admin_value post_max_size 10M
        php_admin_value max_execution_time 300
        php_admin_value memory_limit 256M
    </Directory>
    
    # 禁止访问敏感目录
    <Directory /var/www/TaskMgr/config>
        Require all denied
    </Directory>
    
    <Directory /var/www/TaskMgr/src>
        Require all denied
    </Directory>
    
    <Directory /var/www/TaskMgr/logs>
        Require all denied
    </Directory>
    
    # 日志配置
    ErrorLog /var/log/httpd/taskmgr_error.log
    CustomLog /var/log/httpd/taskmgr_access.log combined
    LogLevel warn
</VirtualHost>

# HTTPS配置（推荐）
<VirtualHost *:443>
    ServerName taskmgr.yourdomain.com
    DocumentRoot /var/www/TaskMgr/public
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # 其他配置同上...
</VirtualHost>
```

#### Nginx配置
创建配置文件 `/etc/nginx/conf.d/taskmgr.conf`:
```nginx
server {
    listen 80;
    server_name taskmgr.yourdomain.com;
    root /var/www/TaskMgr/public;
    index index.php index.html;
    
    # 安全设置
    server_tokens off;
    
    # 日志配置
    access_log /var/log/nginx/taskmgr_access.log;
    error_log /var/log/nginx/taskmgr_error.log;
    
    # 主要位置配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 安全设置
        fastcgi_param PHP_VALUE "upload_max_filesize=10M \n post_max_size=10M \n max_execution_time=300 \n memory_limit=256M";
    }
    
    # 禁止访问敏感文件
    location ~ /\.(ht|git) {
        deny all;
    }
    
    location ~ ^/(config|src|logs|database)/ {
        deny all;
    }
    
    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS配置（推荐）
server {
    listen 443 ssl http2;
    server_name taskmgr.yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置同上...
}
```

### 4. 重启Web服务器
```bash
# Apache
sudo systemctl restart httpd

# Nginx
sudo systemctl restart nginx
```

## Cron配置

### 1. 配置系统Cron
```bash
# 编辑crontab
sudo crontab -e

# 添加TaskMgr任务（每分钟检查一次）
* * * * * /usr/bin/php /var/www/TaskMgr/cron.php >> /var/www/TaskMgr/logs/cron.log 2>&1

# 添加日志清理任务（每天凌晨清理30天前的日志）
0 2 * * * find /var/www/TaskMgr/logs -name "*.log" -mtime +30 -delete
```

### 2. 验证Cron配置
```bash
# 查看crontab
sudo crontab -l

# 检查cron服务状态
sudo systemctl status crond

# 手动测试cron脚本
cd /var/www/TaskMgr
sudo php cron.php
```

## 安全加固

### 1. 防火墙配置
```bash
# 只允许必要端口
sudo firewall-cmd --permanent --remove-service=ssh  # 如果不需要SSH
sudo firewall-cmd --permanent --add-port=22/tcp     # SSH端口（如果需要）
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SELinux配置
```bash
# 检查SELinux状态
getenforce

# 如果启用了SELinux，设置适当的上下文
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P httpd_can_network_connect_db 1

# 设置文件上下文
sudo semanage fcontext -a -t httpd_exec_t "/var/www/TaskMgr/public(/.*)?"
sudo restorecon -R /var/www/TaskMgr/public
```

### 3. 文件权限加固
```bash
# 设置严格的文件权限
sudo chmod 750 /var/www/TaskMgr
sudo chmod 750 /var/www/TaskMgr/config
sudo chmod 640 /var/www/TaskMgr/config/*.php
sudo chmod 750 /var/www/TaskMgr/logs
sudo chmod 755 /var/www/TaskMgr/public
sudo chmod 644 /var/www/TaskMgr/public/*.php
```

## 监控和维护

### 1. 日志轮转配置
创建 `/etc/logrotate.d/taskmgr`:
```
/var/www/TaskMgr/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 apache apache
    postrotate
        /bin/systemctl reload httpd > /dev/null 2>&1 || true
    endscript
}
```

### 2. 系统监控
```bash
# 创建监控脚本
sudo cat > /usr/local/bin/taskmgr-monitor.sh << 'EOF'
#!/bin/bash
# TaskMgr监控脚本

LOG_FILE="/var/log/taskmgr-monitor.log"
TASKMGR_DIR="/var/www/TaskMgr"

# 检查Web服务
if ! systemctl is-active --quiet httpd; then
    echo "$(date): Apache服务异常" >> $LOG_FILE
    systemctl restart httpd
fi

# 检查数据库服务
if ! systemctl is-active --quiet mariadb; then
    echo "$(date): MariaDB服务异常" >> $LOG_FILE
    systemctl restart mariadb
fi

# 检查磁盘空间
DISK_USAGE=$(df $TASKMGR_DIR | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查日志文件大小
find $TASKMGR_DIR/logs -name "*.log" -size +100M -exec echo "$(date): 日志文件过大: {}" \; >> $LOG_FILE
EOF

sudo chmod +x /usr/local/bin/taskmgr-monitor.sh

# 添加到crontab（每5分钟检查一次）
echo "*/5 * * * * /usr/local/bin/taskmgr-monitor.sh" | sudo crontab -
```

### 3. 备份策略
```bash
# 创建备份脚本
sudo cat > /usr/local/bin/taskmgr-backup.sh << 'EOF'
#!/bin/bash
# TaskMgr备份脚本

BACKUP_DIR="/backup/taskmgr"
TASKMGR_DIR="/var/www/TaskMgr"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u taskmgr_user -p'your_password' taskmgr > $BACKUP_DIR/taskmgr_db_$DATE.sql

# 备份配置文件
tar -czf $BACKUP_DIR/taskmgr_config_$DATE.tar.gz -C $TASKMGR_DIR config

# 清理30天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

sudo chmod +x /usr/local/bin/taskmgr-backup.sh

# 添加到crontab（每天凌晨3点备份）
echo "0 3 * * * /usr/local/bin/taskmgr-backup.sh" | sudo crontab -
```

## 故障排除

### 常见问题和解决方案

1. **Web界面500错误**
   ```bash
   # 检查错误日志
   sudo tail -f /var/log/httpd/taskmgr_error.log
   
   # 检查PHP错误
   sudo tail -f /var/log/php-fpm/www-error.log
   
   # 检查文件权限
   ls -la /var/www/TaskMgr/
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   mysql -u taskmgr_user -p taskmgr
   
   # 检查数据库服务
   sudo systemctl status mariadb
   
   # 查看数据库日志
   sudo tail -f /var/log/mariadb/mariadb.log
   ```

3. **Cron任务不执行**
   ```bash
   # 检查cron服务
   sudo systemctl status crond
   
   # 查看cron日志
   sudo tail -f /var/log/cron
   
   # 手动测试
   cd /var/www/TaskMgr && php cron.php
   ```

## 性能优化

### 1. PHP优化
编辑 `/etc/php.ini`:
```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M

# OPcache配置
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
```

### 2. 数据库优化
```sql
-- 添加索引
ALTER TABLE taskmgr_task_logs ADD INDEX idx_start_time_status (start_time, status);
ALTER TABLE taskmgr_tasks ADD INDEX idx_status_next_run (status, next_run_time);

-- 定期清理旧日志
DELETE FROM taskmgr_task_logs WHERE start_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 3. 系统优化
```bash
# 调整系统参数
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.rmem_max=16777216' >> /etc/sysctl.conf
echo 'net.core.wmem_max=16777216' >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

部署完成后，访问 `http://your-domain` 即可使用TaskMgr计划任务管理器。
