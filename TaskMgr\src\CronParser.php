<?php
/**
 * Cron表达式解析器
 */

class CronParser {
    
    /**
     * 解析Cron表达式并获取下次执行时间
     */
    public function getNextRunTime($cronExpression, $fromTime = null) {
        if ($fromTime === null) {
            $fromTime = time();
        }
        
        $parts = explode(' ', trim($cronExpression));
        
        if (count($parts) !== 5) {
            throw new InvalidArgumentException("无效的Cron表达式: $cronExpression");
        }
        
        list($minute, $hour, $day, $month, $weekday) = $parts;
        
        // 从下一分钟开始计算
        $nextTime = $fromTime + 60;
        $nextTime = $nextTime - ($nextTime % 60); // 对齐到分钟
        
        // 最多查找未来一年
        $maxTime = $fromTime + (365 * 24 * 60 * 60);
        
        while ($nextTime <= $maxTime) {
            $timeInfo = getdate($nextTime);
            
            if ($this->matchesCronField($minute, $timeInfo['minutes']) &&
                $this->matchesCronField($hour, $timeInfo['hours']) &&
                $this->matchesCronField($day, $timeInfo['mday']) &&
                $this->matchesCronField($month, $timeInfo['mon']) &&
                $this->matchesCronField($weekday, $timeInfo['wday'])) {
                
                return date('Y-m-d H:i:s', $nextTime);
            }
            
            $nextTime += 60; // 增加一分钟
        }
        
        return null; // 未找到匹配的时间
    }
    
    /**
     * 检查时间字段是否匹配Cron字段
     */
    private function matchesCronField($cronField, $timeValue) {
        // 处理通配符
        if ($cronField === '*') {
            return true;
        }
        
        // 处理步长值 (如 */5)
        if (strpos($cronField, '*/') === 0) {
            $step = (int)substr($cronField, 2);
            return $timeValue % $step === 0;
        }
        
        // 处理范围 (如 1-5)
        if (strpos($cronField, '-') !== false) {
            list($start, $end) = explode('-', $cronField);
            return $timeValue >= (int)$start && $timeValue <= (int)$end;
        }
        
        // 处理列表 (如 1,3,5)
        if (strpos($cronField, ',') !== false) {
            $values = explode(',', $cronField);
            return in_array($timeValue, array_map('intval', $values));
        }
        
        // 处理具体值
        return $timeValue == (int)$cronField;
    }
    
    /**
     * 验证Cron表达式是否有效
     */
    public function isValidCronExpression($cronExpression) {
        $parts = explode(' ', trim($cronExpression));
        
        if (count($parts) !== 5) {
            return false;
        }
        
        list($minute, $hour, $day, $month, $weekday) = $parts;
        
        // 验证各个字段
        if (!$this->isValidCronField($minute, 0, 59) ||
            !$this->isValidCronField($hour, 0, 23) ||
            !$this->isValidCronField($day, 1, 31) ||
            !$this->isValidCronField($month, 1, 12) ||
            !$this->isValidCronField($weekday, 0, 7)) { // 0和7都表示周日
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证单个Cron字段是否有效
     */
    private function isValidCronField($field, $min, $max) {
        // 通配符
        if ($field === '*') {
            return true;
        }
        
        // 步长值
        if (strpos($field, '*/') === 0) {
            $step = substr($field, 2);
            return is_numeric($step) && (int)$step > 0 && (int)$step <= $max;
        }
        
        // 范围
        if (strpos($field, '-') !== false) {
            $parts = explode('-', $field);
            if (count($parts) !== 2) {
                return false;
            }
            $start = (int)$parts[0];
            $end = (int)$parts[1];
            return $start >= $min && $end <= $max && $start <= $end;
        }
        
        // 列表
        if (strpos($field, ',') !== false) {
            $values = explode(',', $field);
            foreach ($values as $value) {
                if (!is_numeric($value) || (int)$value < $min || (int)$value > $max) {
                    return false;
                }
            }
            return true;
        }
        
        // 具体值
        return is_numeric($field) && (int)$field >= $min && (int)$field <= $max;
    }
    
    /**
     * 获取Cron表达式的人类可读描述
     */
    public function getDescription($cronExpression) {
        if (!$this->isValidCronExpression($cronExpression)) {
            return "无效的Cron表达式";
        }
        
        $parts = explode(' ', trim($cronExpression));
        list($minute, $hour, $day, $month, $weekday) = $parts;
        
        $description = [];
        
        // 分钟
        if ($minute === '*') {
            $description[] = "每分钟";
        } elseif (strpos($minute, '*/') === 0) {
            $step = substr($minute, 2);
            $description[] = "每{$step}分钟";
        } else {
            $description[] = "在第{$minute}分钟";
        }
        
        // 小时
        if ($hour === '*') {
            $description[] = "每小时";
        } elseif (strpos($hour, '*/') === 0) {
            $step = substr($hour, 2);
            $description[] = "每{$step}小时";
        } else {
            $description[] = "在{$hour}点";
        }
        
        // 日期
        if ($day !== '*') {
            $description[] = "每月第{$day}天";
        }
        
        // 月份
        if ($month !== '*') {
            $description[] = "在{$month}月";
        }
        
        // 星期
        if ($weekday !== '*') {
            $weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            if (is_numeric($weekday)) {
                $description[] = "在{$weekNames[$weekday]}";
            }
        }
        
        return implode(' ', $description);
    }
    
    /**
     * 获取常用的Cron表达式示例
     */
    public static function getExamples() {
        return [
            '* * * * *' => '每分钟执行',
            '*/5 * * * *' => '每5分钟执行',
            '0 * * * *' => '每小时执行',
            '0 */2 * * *' => '每2小时执行',
            '0 0 * * *' => '每天午夜执行',
            '0 2 * * *' => '每天凌晨2点执行',
            '0 0 * * 0' => '每周日午夜执行',
            '0 0 1 * *' => '每月1号午夜执行',
            '0 0 1 1 *' => '每年1月1号午夜执行',
            '30 2 * * 1-5' => '工作日凌晨2:30执行',
            '0 9,17 * * 1-5' => '工作日上午9点和下午5点执行'
        ];
    }
}
