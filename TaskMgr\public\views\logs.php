<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看 - TaskMgr</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 4px;
            white-space: pre-wrap;
        }
        .log-entry.success {
            border-left: 4px solid #198754;
        }
        .log-entry.failed {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }
        .log-entry.running {
            border-left: 4px solid #0d6efd;
            background-color: #cff4fc;
        }
        .log-detail {
            max-height: 400px;
            overflow-y: auto;
        }
        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-clock-history"></i> TaskMgr
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">仪表板</a>
                <a class="nav-link" href="/tasks">任务管理</a>
                <a class="nav-link active" href="/logs">日志查看</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>日志查看</h2>

        <!-- 标签页 -->
        <ul class="nav nav-tabs mt-4" id="logTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="task-logs-tab" data-bs-toggle="tab" data-bs-target="#task-logs" type="button" role="tab">
                    <i class="bi bi-list-task"></i> 任务执行日志
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-logs-tab" data-bs-toggle="tab" data-bs-target="#system-logs" type="button" role="tab">
                    <i class="bi bi-gear"></i> 系统日志
                </button>
            </li>
        </ul>

        <div class="tab-content" id="logTabContent">
            <!-- 任务执行日志 -->
            <div class="tab-pane fade show active" id="task-logs" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-md-2">
                                <select class="form-select form-select-sm" id="taskFilter" onchange="loadTaskLogs()">
                                    <option value="">所有任务</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select form-select-sm" id="statusFilter" onchange="loadTaskLogs()">
                                    <option value="">所有状态</option>
                                    <option value="success">成功</option>
                                    <option value="failed">失败</option>
                                    <option value="running">运行中</option>
                                    <option value="timeout">超时</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control form-control-sm" id="startDate" onchange="loadTaskLogs()">
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control form-control-sm" id="endDate" onchange="loadTaskLogs()">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select form-select-sm" id="limitSelect" onchange="loadTaskLogs()">
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                    <option value="200">200条</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadTaskLogs()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>任务名称</th>
                                        <th>状态</th>
                                        <th>开始时间</th>
                                        <th>耗时</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="taskLogsTableBody">
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="spinner-border spinner-border-sm" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav id="taskLogsPagination" class="mt-3" style="display: none;">
                            <ul class="pagination pagination-sm justify-content-center">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 系统日志 -->
            <div class="tab-pane fade" id="system-logs" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-md-2">
                                <input type="date" class="form-control form-control-sm" id="systemLogDate" onchange="loadSystemLogs()">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select form-select-sm" id="logLevelFilter" onchange="loadSystemLogs()">
                                    <option value="">所有级别</option>
                                    <option value="debug">DEBUG</option>
                                    <option value="info">INFO</option>
                                    <option value="warning">WARNING</option>
                                    <option value="error">ERROR</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control form-control-sm" id="logKeyword" placeholder="搜索关键词..." onkeyup="searchSystemLogs()">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select form-select-sm" id="logLinesSelect" onchange="loadSystemLogs()">
                                    <option value="100">100行</option>
                                    <option value="200">200行</option>
                                    <option value="500">500行</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="loadSystemLogs()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="systemLogsContent" class="log-detail">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">执行详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="logDetailContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 0;
        let totalPages = 0;
        let pageSize = 50;

        // 加载任务列表（用于筛选）
        async function loadTasksForFilter() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();
                
                if (data.success) {
                    const select = document.getElementById('taskFilter');
                    select.innerHTML = '<option value="">所有任务</option>';
                    
                    data.data.forEach(task => {
                        const option = document.createElement('option');
                        option.value = task.id;
                        option.textContent = task.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载任务列表失败:', error);
            }
        }

        // 加载任务执行日志
        async function loadTaskLogs(page = 0) {
            try {
                const taskId = document.getElementById('taskFilter').value;
                const status = document.getElementById('statusFilter').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const limit = document.getElementById('limitSelect').value;
                
                const params = new URLSearchParams();
                if (taskId) params.append('task_id', taskId);
                if (status) params.append('status', status);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                params.append('limit', limit);
                params.append('offset', page * limit);
                
                const response = await fetch(`/api/logs/task-logs?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    renderTaskLogs(data.data.logs);
                    updatePagination(data.data.total, parseInt(limit), page);
                } else {
                    showAlert('加载日志失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('加载任务日志失败:', error);
                showAlert('加载任务日志失败', 'danger');
            }
        }

        // 渲染任务日志
        function renderTaskLogs(logs) {
            const tbody = document.getElementById('taskLogsTableBody');
            
            if (logs.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无日志</td></tr>';
                return;
            }
            
            tbody.innerHTML = logs.map(log => `
                <tr>
                    <td>${log.task_name || '未知任务'}</td>
                    <td><span class="badge bg-${getLogStatusColor(log.status)}">${getLogStatusText(log.status)}</span></td>
                    <td>${new Date(log.start_time).toLocaleString()}</td>
                    <td>${log.duration ? log.duration + 's' : '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="showLogDetail(${log.id})">
                            <i class="bi bi-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新分页
        function updatePagination(total, limit, currentPageNum) {
            totalPages = Math.ceil(total / limit);
            currentPage = currentPageNum;
            
            const pagination = document.getElementById('taskLogsPagination');
            const ul = pagination.querySelector('ul');
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }
            
            pagination.style.display = 'block';
            ul.innerHTML = '';
            
            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 0 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadTaskLogs(${currentPage - 1})">上一页</a>`;
            ul.appendChild(prevLi);
            
            // 页码
            const startPage = Math.max(0, currentPage - 2);
            const endPage = Math.min(totalPages - 1, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadTaskLogs(${i})">${i + 1}</a>`;
                ul.appendChild(li);
            }
            
            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadTaskLogs(${currentPage + 1})">下一页</a>`;
            ul.appendChild(nextLi);
        }

        // 获取日志状态颜色
        function getLogStatusColor(status) {
            const colors = {
                'success': 'success',
                'failed': 'danger',
                'running': 'primary',
                'timeout': 'warning',
                'cancelled': 'secondary'
            };
            return colors[status] || 'secondary';
        }

        // 获取日志状态文本
        function getLogStatusText(status) {
            const texts = {
                'success': '成功',
                'failed': '失败',
                'running': '运行中',
                'timeout': '超时',
                'cancelled': '已取消'
            };
            return texts[status] || status;
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 显示日志详情
        async function showLogDetail(logId) {
            try {
                const response = await fetch(`/api/logs/execution-detail?log_id=${logId}`);
                const data = await response.json();

                if (data.success) {
                    const log = data.data;
                    const content = document.getElementById('logDetailContent');

                    content.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td>任务名称:</td><td>${log.task_name}</td></tr>
                                    <tr><td>执行ID:</td><td><code>${log.execution_id}</code></td></tr>
                                    <tr><td>状态:</td><td><span class="badge bg-${getLogStatusColor(log.status)}">${getLogStatusText(log.status)}</span></td></tr>
                                    <tr><td>开始时间:</td><td>${new Date(log.start_time).toLocaleString()}</td></tr>
                                    <tr><td>结束时间:</td><td>${log.end_time ? new Date(log.end_time).toLocaleString() : '-'}</td></tr>
                                    <tr><td>执行时长:</td><td>${log.duration ? log.duration + 's' : '-'}</td></tr>
                                    <tr><td>退出代码:</td><td>${log.exit_code !== null ? log.exit_code : '-'}</td></tr>
                                    <tr><td>内存使用:</td><td>${log.memory_usage ? log.memory_usage + 'KB' : '-'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>执行命令</h6>
                                <pre class="bg-light p-2 rounded">${log.command || '未知命令'}</pre>
                            </div>
                        </div>

                        ${log.output ? `
                        <div class="mt-3">
                            <h6>执行输出</h6>
                            <pre class="bg-light p-3 rounded log-detail">${log.output}</pre>
                        </div>
                        ` : ''}

                        ${log.error_message ? `
                        <div class="mt-3">
                            <h6>错误信息</h6>
                            <pre class="bg-danger text-white p-3 rounded log-detail">${log.error_message}</pre>
                        </div>
                        ` : ''}
                    `;

                    new bootstrap.Modal(document.getElementById('logDetailModal')).show();
                } else {
                    showAlert('加载日志详情失败: ' + data.message, 'danger');
                }
            } catch (error) {
                console.error('加载日志详情失败:', error);
                showAlert('加载日志详情失败', 'danger');
            }
        }

        // 加载系统日志
        async function loadSystemLogs() {
            try {
                const date = document.getElementById('systemLogDate').value;
                const level = document.getElementById('logLevelFilter').value;
                const lines = document.getElementById('logLinesSelect').value;

                const params = new URLSearchParams();
                if (date) params.append('date', date);
                if (level) params.append('level', level);
                params.append('lines', lines);

                const response = await fetch(`/api/logs/system-logs?${params}`);
                const data = await response.json();

                if (data.success) {
                    renderSystemLogs(data.data);
                } else {
                    document.getElementById('systemLogsContent').innerHTML =
                        '<p class="text-danger">加载系统日志失败: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('加载系统日志失败:', error);
                document.getElementById('systemLogsContent').innerHTML =
                    '<p class="text-danger">加载系统日志失败</p>';
            }
        }

        // 渲染系统日志
        function renderSystemLogs(logs) {
            const container = document.getElementById('systemLogsContent');

            if (logs.length === 0) {
                container.innerHTML = '<p class="text-muted">暂无日志</p>';
                return;
            }

            container.innerHTML = logs.map(line => {
                let className = 'log-entry';
                if (line.includes('[ERROR]')) {
                    className += ' failed';
                } else if (line.includes('[WARNING]')) {
                    className += ' text-warning';
                } else if (line.includes('[INFO]')) {
                    className += ' success';
                }

                return `<div class="${className}">${line}</div>`;
            }).join('');
        }

        // 搜索系统日志
        async function searchSystemLogs() {
            const keyword = document.getElementById('logKeyword').value;
            if (!keyword || keyword.length < 2) {
                loadSystemLogs();
                return;
            }

            try {
                const date = document.getElementById('systemLogDate').value;
                const level = document.getElementById('logLevelFilter').value;

                const params = new URLSearchParams();
                params.append('keyword', keyword);
                if (date) params.append('date', date);
                if (level) params.append('level', level);
                params.append('lines', 100);

                const response = await fetch(`/api/logs/system-logs?${params}`);
                const data = await response.json();

                if (data.success) {
                    renderSearchResults(data.data);
                } else {
                    document.getElementById('systemLogsContent').innerHTML =
                        '<p class="text-danger">搜索失败: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('搜索系统日志失败:', error);
            }
        }

        // 渲染搜索结果
        function renderSearchResults(results) {
            const container = document.getElementById('systemLogsContent');

            if (results.length === 0) {
                container.innerHTML = '<p class="text-muted">未找到匹配的日志</p>';
                return;
            }

            container.innerHTML = results.map(result => {
                let className = 'log-entry';
                if (result.line.includes('[ERROR]')) {
                    className += ' failed';
                } else if (result.line.includes('[WARNING]')) {
                    className += ' text-warning';
                } else if (result.line.includes('[INFO]')) {
                    className += ' success';
                }

                return `
                    <div class="${className}">
                        <small class="text-muted">${result.file} - ${result.timestamp}</small><br>
                        ${result.line}
                    </div>
                `;
            }).join('');
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('systemLogDate').value = today;

            loadTasksForFilter();
            loadTaskLogs();
            loadSystemLogs();

            // 监听标签页切换
            document.getElementById('system-logs-tab').addEventListener('shown.bs.tab', function() {
                loadSystemLogs();
            });
        });
    </script>
</body>
</html>
